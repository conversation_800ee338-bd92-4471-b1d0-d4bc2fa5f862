#!/usr/bin/env python3
"""
Power-cycle a specific list of VMs by name.

- Reads vCenter connection variables from C:\\Users\\<USER>\\vscode\\txt\\vsphere_variable.txt
  keys: host, user, password (space-separated: key value)
- Loops a fixed list of VMs and power cycles each (off then on), with throttling.
- --dry-run prints the intended operations and skips connecting to vCenter.

This script is self-contained and does not require the tools.* helpers.
"""
import argparse
import atexit
import ssl
import sys
import time
from pathlib import Path
from typing import Dict, List, Optional

from pyVim import connect
from pyVmomi import vim

# Hardcoded list as requested
VMS: List[str] = ['bct1-004d4d']


def parse_vsphere_vars_file(file_path: str) -> Dict[str, str]:
    """Parse key-value pairs from the variables file.

    Lines: "key value" separated by a single space. Lines starting with '#' are ignored.
    The value can contain spaces; everything after the first space belongs to the value.
    """
    data: Dict[str, str] = {}
    with open(file_path, "r", encoding="utf-8") as fh:
        for raw in fh:
            line = raw.strip()
            if not line or line.startswith("#"):
                continue
            parts = line.split()
            if len(parts) < 2:
                continue
            key = parts[0].strip()
            value = " ".join(parts[1:]).strip()
            data[key] = value
    return data


def resolve_credentials(args: argparse.Namespace) -> Dict[str, Optional[str]]:
    """Resolve host/user/password from CLI or variables file.

    CLI overrides file values. May return None values (acceptable in dry-run).
    """
    vars_path = Path(r"C:\\Users\\<USER>\\vscode\\txt\\vsphere_variable.txt")
    kv: Dict[str, str] = {}
    if vars_path.exists():
        try:
            kv = parse_vsphere_vars_file(str(vars_path))
        except Exception as ex:
            print("Warning: failed to parse variables file: {0}".format(ex))

    host: Optional[str] = args.host or kv.get("host")
    user: Optional[str] = args.user or kv.get("user")
    password: Optional[str] = args.password or kv.get("password")
    return {"host": host, "user": user, "password": password}


def wait_for_tasks(tasks: List[vim.Task]) -> None:
    """Wait until all tasks finish successfully or raise on error."""
    remaining = list(tasks)
    while remaining:
        pending: List[vim.Task] = []
        for task in remaining:
            info = task.info
            state = info.state
            if state == vim.TaskInfo.State.success:
                continue
            if state == vim.TaskInfo.State.error:
                msg = getattr(info.error, 'msg', str(info.error))
                raise RuntimeError("Task failed: {0}".format(msg))
            pending.append(task)
        remaining = pending
        if remaining:
            time.sleep(1)


def get_obj_by_name(content, vimtype: List[type], name: str):
    """Return first managed object with the given name across inventory, else None."""
    view = content.viewManager.CreateContainerView(content.rootFolder, vimtype, True)
    try:
        for obj in view.view:
            if getattr(obj, 'name', None) == name:
                return obj
    finally:
        view.Destroy()
    return None


def power_cycle_vm(vm: vim.VirtualMachine, throttle_seconds: int, skip_if_off: bool = False) -> None:
    """Power off (if on) then power on the VM, with throttling between operations."""
    name = vm.name
    state = getattr(vm.runtime, 'powerState', None)
    print("Processing VM: {0} (state: {1})".format(name, state))

    # Skip templates entirely
    try:
        if bool(vm.config and vm.config.template):
            print("- Skipping template: {0}".format(name))
            return
    except Exception:
        pass

    # Power off when needed
    if state == vim.VirtualMachinePowerState.poweredOn:
        print("- Powering off VM: {0}".format(name))
        off_task = vm.PowerOffVM_Task()
        wait_for_tasks([off_task])
        print("- Powered off: {0}".format(name))
    else:
        print("- VM already powered off: {0}".format(name))
        if skip_if_off:
            print("- Skipping power on for VM (skip-if-off enabled): {0}".format(name))
            return

    if throttle_seconds > 0:
        print("Sleeping for {0} seconds to throttle operations...".format(throttle_seconds))
        time.sleep(throttle_seconds)

    print("- Powering on VM: {0}".format(name))
    on_task = vm.PowerOnVM_Task()
    wait_for_tasks([on_task])
    print("- Powered on: {0}".format(name))

    if throttle_seconds > 0:
        print("Sleeping for {0} seconds to throttle operations...".format(throttle_seconds))
        time.sleep(throttle_seconds)


def connect_vcenter(host: str, user: str, password: str):
    """Connect to vCenter and return a ServiceInstance with SSL verification disabled."""
    if not host or not user or not password:
        raise SystemExit("Missing vCenter connection parameters (host/user/password).")

    ctx = ssl.create_default_context()
    ctx.check_hostname = False
    ctx.verify_mode = ssl.CERT_NONE
    si = connect.SmartConnect(host=host, user=user, pwd=password, sslContext=ctx)

    atexit.register(connect.Disconnect, si)
    return si


def main() -> int:
    parser = argparse.ArgumentParser(description="Power cycle selected VMs by name.")
    parser.add_argument("--sleep-seconds", type=int, default=60, help="Throttle delay between operations")
    parser.add_argument("--dry-run", action="store_true", help="Print actions without connecting")
    parser.add_argument("--skip-if-off", action="store_true", help="Skip power on when already off")
    parser.add_argument("-s", "--host", dest="host", required=False, help="vCenter hostname/IP")
    parser.add_argument("-u", "--user", dest="user", required=False, help="vCenter username")
    parser.add_argument("-p", "--password", dest="password", required=False, help="vCenter password")
    args = parser.parse_args()

    creds = resolve_credentials(args)

    print("Target VMs: {0}".format(", ".join(VMS) if VMS else "<none>"))

    if args.dry_run:
        print("Dry run selected; will not connect to vCenter.")
        print("Host: {0}".format(creds.get("host")))
        print("User: {0}".format(creds.get("user")))
        print("Actions: For each VM -> power off (if on), then power on, with sleep {0}s.".format(args.sleep_seconds))
        return 0

    # Not a dry-run: must have credentials
    if not creds.get("host") or not creds.get("user") or not creds.get("password"):
        print("Error: host/user/password must be provided via CLI or variables file.")
        return 2

    try:
        si = connect_vcenter(
            host=str(creds["host"]),
            user=str(creds["user"]),
            password=str(creds["password"]),
        )
    except Exception as ex:
        print("Failed to connect to vCenter: {0}".format(ex))
        return 1

    content = si.RetrieveContent()

    failures = 0
    for vm_name in VMS:
        try:
            print("\n--- Handling VM: {0} ---".format(vm_name))
            vm = get_obj_by_name(content, [vim.VirtualMachine], vm_name)
            if vm is None:
                print("WARNING: VM not found: {0}".format(vm_name))
                continue
            power_cycle_vm(vm, args.sleep_seconds, skip_if_off=args.skip_if_off)
        except Exception as ex:
            failures += 1
            print("ERROR while processing '{0}': {1}".format(vm_name, ex))

    print("\nDone. Failures: {0}".format(failures))
    return 0 if failures == 0 else 3


if __name__ == "__main__":
    sys.exit(main())
