<PERSON>

  Yesterday at 3:12 PM

Starting a different thread related to <PERSON>'s above: can we formulate exactly what service level objective we are looking to hit: I get that it is

builds that are delivered to QV on time for this first shift and include check-ins from the previous day up to 16:30 UK time.

But what do we exactly mean by that?

Which branches are these CLs from - CH1-Content-dev and CH1-SP-Content-Dev? Or just one of them?

16:30 UK time the day before (so 15:30 UTC)

When does the first QV shift start, in UTC?

Are we looking to have those builds delivered from Shift by that time, or is it acceptable to have builds in Shift and available for delivery by that time?

20 replies



<PERSON>

  Yesterday at 3:31 PM

QV is testing combined builds so we need both branches to have green builds. If one is green but not the other, it blocks them:

CH1-Content-dev & CH1-SP-Content-Dev

Next week we need to monitor for Day 0 as the Singleplayer team will be working mostly there:

CH1-stage & CH1-SP-stage



<PERSON>

  Yesterday at 3:33 PM

So would you say we want to include all CLs before the cutoff time from both CH1-Content-dev & CH1-SP-Content-Dev (or the equivalent Stage branches)

3:34

Sounds like the QV shift starts at 06:00 UTC

So that gives us 14.5 hours from 15:30 --> 06:00 UTC to complete all the various CI jobs and finish Shift uploads. (edited) 



<PERSON>

  Yesterday at 3:37 PM

We integrate data from CH1-content-dev to CH1-SP-content-dev via:

CH1-content-dev.copy-integrate-to.CH1-SP-content-dev.start/So in the scenario where:

CH1-content-dev is broken

CH1-content-dev to CH1-SP-content-dev integration is green

CH1-SP-content-dev is greenCH1-content-dev.frosty.start must be ran manually on older changelist (affects MP. exe)  as upstream jobs are broken, but job can take on newer SP-content-dev changelist

QV can test and regress bugs with a fresh SP.exe

That is what we did for Aug 4th to Aug 6 when CH1-content-dev.deployment.start job was red (edited) 



Viktor Raykov

  Yesterday at 3:44 PM

the frosty job is ran on the main stream CH1-content-dev

So if the stream is having build errors it impacts the full delivery for SPThere are ways to manually intervene and trigger the jobs to at least produce some results, but its rare we do this

image.png

 







Mark Baker

  Yesterday at 3:48 PM

That's a good point -- in this case we're specifically interested in the SP stream and QV testing that.I wonder if it would make sense to flip the combined builds around a bit and trigger them from the SP stream instead, at least for the QV builds?

So have frosty do combine on the SP stream, and have shift do an upload of just the combined SP stream.That way if the dev line breaks but SP is still working, then it's all still fine?



Danny Isaac

  Yesterday at 4:01 PM

"Sounds like the QV shift starts at 06:00 UTC

So that gives us 14.5 hours from 15:30 --> 06:00 UTC to complete all the various CI jobs and finish Shift uploads. (edited)"Yes that sounds about right. @cbogdan Any idea how long the shift subscription takes to end up on QV machines?



Mark Baker

  Yesterday at 4:02 PM

Just saw in the other thread that the Shift subscription kicks off at 4:30 UTC

4:03

so 15:30 --> 04:30 UTC gives us 13 hours to have completed Shift upload for the subscription to then pick up the new build (edited) 

1





Danny Isaac

  Yesterday at 4:03 PM

Great. So sounds like we'd need the build locked in by then.



Viktor Raykov

  Yesterday at 4:03 PM

I like that idea, but maybe there is still a small caveat to workoutThe combined builds need the MP build which would be deployed from CH1-content-dev. So there is a dependency from SP-content-dev on it. But if the MP build is failing we can still make the combined build with a fresh SP exe, just using an old MP



Mark Baker

  Yesterday at 4:05 PM

So potentially we need a timer on a Frosty start job which just picks the latest successful SP + MP builds and combines them, unless they have already been built.

4:06

That way there would always be something for the Shift start job to upload

4:10

Once we have the improvement to Shift.Start it should have a more predictable duration, today it varies up to 7 hrs bur I think 3hr is more correct.  Then we could look at how long the Frosty builds take and figure out when to schedule the 'backup' SP job for.



Viktor Raykov

  Yesterday at 5:00 PM

@kkalenderoglu and I did a quick practiceCH1-content-dev

frosty (without combined)

shift.upload

CH1-SP-content-dev

frosty (only combined) - uses latest successful

frosty from above

Scenario: As a SP content creator I am the last submitter at 16.30 UK - CL 12345 is on CH1-content-dev

    1. CL 12345 has to go through auto integration CH1-content-dev --> CH1-SP-content-dev -- avg 15 min

    2. CL 12345 has to go through CH1-SP-content-dev.code.start -- avg 50 min

    3. CL 12345 has to go through CH1-SP-content-dev.data.start -- avg 1 hr

    4. CL 12345 has to go through CH1-SP-content-dev.deployment-data.start -- avg 2 hrs

    ---- (new setup proposed from here)

    5. CL 12345 has to go through CH1-content-dev.frosty.combined -- avg 2 hrs

    6. (scheduled) CL 12345 has to go through CH1-content-dev.shift.start  -- avg 4 hrsTotal 10 hrsSo from 16:30 + 10 hrs = 2:30 AM build completion in best scenario, no errorsQV comes in the morning and has the builds (edited) 



Viktor Raykov

  Yesterday at 5:07 PM

How does this look from your perspective Mark based on your suggestion of having frosty do combine on the SP stream?Only  I have is if we need to have new job on CH1-SP-content-dev or SP-content-dev will trigger the equivalent job we already have on CH1-content-dev if that makes sense  (edited) 



Mark Baker

  Yesterday at 5:13 PM

I think it looks reasonable. We could trigger the equivalent job in CH1-content-dev as you say.  We might need a new frosty.SP-combined.start job that invokes just the combined frosty jobs?



Kaan Kalenderoglu

  Yesterday at 5:45 PM

Throwing an idea here: Would it make sense to separate combine frosty jobs to .combine.frosty.start ?

We can configure in a way that it is triggered after successful CH1-content-dev.frosty.start (takes last successful SP deployment-data CL) and after CH1-SP-content-dev.deployment-data.start (takes last successful MP frosty start CL) + once a day scheduled trigger to use last successful CLs from both jobs to make sure there is a build available with latest successful CLs.Also separating frosty and combine frosty may result in a shorter duration for individual shift upload jobs, if we also separate MP shift uploads from combined Shift uploads.

We also eliminate the dependency of MP and SP streams for frosty.start jobs











Mark Baker

  Yesterday at 5:45 PM

Yes - I think it makes sense to split those jobs out

5:46

Especially as we have different delivery time requirements for them; I could imagine having one QV shift testing SP and another testing MP for example.