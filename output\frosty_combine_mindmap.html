<!doctype html>
<html lang="en">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>Frosty Combine Mindmap</title>
  <style>
    body { font-family: Arial, Helvetica, sans-serif; margin: 0; padding: 16px; background:#0b1021; color:#e8e8e8; }
    .container { max-width: 1400px; margin: 0 auto; }
    .card { background: #12172b; border: 1px solid #253059; border-radius: 10px; padding: 12px; }
    pre { white-space: pre-wrap; word-break: break-word; }
  </style>
</head>
<body>
  <div class="container">
    <h1>Frosty Combine Mindmap</h1>
    <p>This page renders the Mermaid mindmap. Requires Internet to fetch Mermaid JS from CDN.</p>
    <div class="card">
      <div class="mermaid" id="mindmap">
      mindmap
        root((JOB: CH1-content-dev.start))
          "JOB: CH1-content-dev.frosty.start (MP build)"
            "ARTIFACT: MP build (exe, assets)"
            "FAILOVER: Use last successful MP when current fails"
            "TRIGGERS"
              "JOB: CH1-content-dev.combine.frosty.start"
          "JOB: .combine.frosty.start (split job)"
            "INPUTS"
              "LATEST: MP from CH1-content-dev.frosty.start"
              "LATEST: SP from CH1-SP-content-dev.deployment-data.start"
            "TRIGGERS"
              "POST-SUCCESS: CH1-content-dev.frosty.start"
              "POST-SUCCESS: CH1-SP-content-dev.deployment-data.start"
              "SCHEDULE: Nightly timer"
            "OUTPUT"
              "ARTIFACT: Combined MP+SP build"
          "JOB: CH1-SP-content-dev.start"
            "JOB: CH1-content-dev.copy-integrate-to.CH1-SP-content-dev.start (~15m)"
            "JOB: CH1-SP-content-dev.code.start (~50m)"
            "JOB: CH1-SP-content-dev.data.start (~1h)"
            "JOB: CH1-SP-content-dev.deployment-data.start (~2h)"
            "SP-TRIGGERED COMBINE"
              "JOB: frosty.SP-combined.start (invoke combine only)"
              "USES last successful MP if needed"
          "SHIFT UPLOAD PATHS"
            "PATH A: MP-only"
              "JOB: CH1-content-dev.shift.upload.MP (~4h)"
              "USE CASE: MP-only QV testing / decoupled deliveries"
            "PATH B: Combined"
              "JOB: CH1-content-dev.shift.upload.combined (~4h)"
              "PREREQ: Combined build ready by 04:30 UTC"
          "SLO & TIMING"
            "CUTOFF: 16:30 UK / 15:30 UTC"
            "SUBSCRIPTION: 04:30 UTC"
            "QV SHIFT: 06:00 UTC"
            "BEST-CASE TOTAL: ~10h"
      </div>
    </div>
    <p>Tip: Use your browser's "Print" or "Save as PDF" to export. For PNG/SVG, see render_mermaid.ps1.</p>
  </div>
  <script type="module">
    import mermaid from 'https://cdn.jsdelivr.net/npm/mermaid@10/dist/mermaid.esm.min.mjs';
    mermaid.initialize({ startOnLoad: true, theme: 'dark' });
  </script>
</body>
</html>