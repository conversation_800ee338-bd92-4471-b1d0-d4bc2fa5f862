#!/usr/bin/env python
"""
Script to power cycle VMs using vSphere API
Reads connection variables from vsphere_variable.txt and cycles through specified VMs
"""

import requests
import time
import sys
import os
from pyVmomi import vim
from tools import cli, service_instance, pchelper, tasks

requests.packages.urllib3.disable_warnings(
    requests.packages.urllib3.exceptions.InsecureRequestWarning)


def read_vsphere_variables(file_path):
    """
    Read vSphere connection variables from file
    
    Args:
        file_path (str): Path to the variable file
        
    Returns:
        dict: Dictionary containing connection variables
    """
    variables = {}
    try:
        with open(file_path, 'r') as f:
            for line in f:
                line = line.strip()
                # Skip comments and empty lines
                if line.startswith('#') or not line:
                    continue
                # Split on first space to handle passwords with spaces
                parts = line.split(' ', 1)
                if len(parts) == 2:
                    key, value = parts
                    variables[key] = value
    except FileNotFoundError:
        print("Error: Variable file not found at {}".format(file_path))
        sys.exit(1)
    except Exception as e:
        print("Error reading variable file: {}".format(e))
        sys.exit(1)
    
    return variables


def create_args_from_variables(variables):
    """
    Create an args object from variables dictionary
    
    Args:
        variables (dict): Dictionary containing connection variables
        
    Returns:
        object: Args object compatible with service_instance.connect()
    """
    class Args:
        def __init__(self):
            self.host = variables.get('host')
            self.user = variables.get('user') 
            self.password = variables.get('password')
            self.port = 443
            self.disable_ssl_verification = True
    
    return Args()


def power_cycle_vm(si, vm_name):
    """
    Power cycle a single VM (power off then power on)
    
    Args:
        si: Service instance object
        vm_name (str): Name of the VM to power cycle
        
    Returns:
        bool: True if successful, False otherwise
    """
    try:
        print("Processing VM: {}".format(vm_name))
        
        # Find the VM
        vm = pchelper.get_obj(si.RetrieveContent(), [vim.VirtualMachine], vm_name)
        if not vm:
            print("  Error: Unable to locate VM '{}'".format(vm_name))
            return False
        
        # Check current power state
        power_state = vm.runtime.powerState
        print("  Current power state: {}".format(power_state))
        
        # Power off if currently on
        if power_state == vim.VirtualMachinePowerState.poweredOn:
            print("  Powering off VM...")
            task = vm.PowerOffVM_Task()
            tasks.wait_for_tasks(si, [task])
            
            if task.info.state == vim.TaskInfo.State.success:
                print("  VM powered off successfully")
            else:
                print("  Error powering off VM: {}".format(task.info.error))
                return False
        
        # Wait a moment before powering on
        print("  Waiting 5 seconds before powering on...")
        time.sleep(5)
        
        # Power on the VM
        print("  Powering on VM...")
        task = vm.PowerOnVM_Task()
        tasks.wait_for_tasks(si, [task])
        
        if task.info.state == vim.TaskInfo.State.success:
            print("  VM powered on successfully")
            print("  Power cycle completed for '{}'".format(vm_name))
            return True
        else:
            print("  Error powering on VM: {}".format(task.info.error))
            return False
            
    except Exception as e:
        print("  Error processing VM '{}': {}".format(vm_name, e))
        return False


def main():
    """
    Main function to power cycle VMs
    """
    # List of VMs to power cycle
    vms = ['bct1-004d4d']
    
    # Path to variable file
    variable_file = os.path.join(os.path.dirname(__file__), '..', '..', 'txt', 'vsphere_variable.txt')
    variable_file = os.path.abspath(variable_file)
    
    print("Reading vSphere variables from: {}".format(variable_file))
    
    # Read connection variables
    variables = read_vsphere_variables(variable_file)
    
    # Validate required variables
    required_vars = ['host', 'user', 'password']
    missing_vars = [var for var in required_vars if var not in variables]
    if missing_vars:
        print("Error: Missing required variables: {}".format(', '.join(missing_vars)))
        sys.exit(1)
    
    print("Connecting to vSphere host: {}".format(variables['host']))
    
    # Create args object and connect
    args = create_args_from_variables(variables)
    si = service_instance.connect(args)
    
    if not si:
        print("Error: Failed to connect to vSphere")
        sys.exit(1)
    
    print("Connected successfully to vSphere")
    print("Starting power cycle process for {} VMs".format(len(vms)))
    print("-" * 50)
    
    # Power cycle each VM
    successful_cycles = 0
    for vm_name in vms:
        if power_cycle_vm(si, vm_name):
            successful_cycles += 1
        print("-" * 50)
    
    # Summary
    print("Power cycle process completed")
    print("Successfully cycled: {}/{} VMs".format(successful_cycles, len(vms)))
    
    if successful_cycles == len(vms):
        print("All VMs processed successfully")
    else:
        print("Some VMs failed to process - check output above for details")
        sys.exit(1)


if __name__ == "__main__":
    main()
