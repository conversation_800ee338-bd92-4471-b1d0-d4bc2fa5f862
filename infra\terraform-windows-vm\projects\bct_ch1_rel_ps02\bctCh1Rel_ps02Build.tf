#*************************************************************
#  Sets up the initial needs to point to our vSphere server
#*************************************************************
# Point to our datacentre
# https://www.terraform.io/docs/providers/vsphere/d/datacenter.html
#
# Notes:
# Datastore Cluster: https://www.terraform.io/docs/providers/vsphere/r/datastore_cluster.html
# Individual Datastores: https://www.terraform.io/docs/providers/vsphere/d/datastore.html
# Resource Pools: https://www.terraform.io/docs/providers/vsphere/d/resource_pool.html
# Networking: https://www.terraform.io/docs/providers/vsphere/d/network.html
# Templates: https://www.terraform.io/docs/providers/vsphere/r/virtual_machine.html
# Folder Managment: https://www.terraform.io/docs/providers/vsphere/r/folder.html
# UUID Usage: https://www.terraform.io/docs/providers/random/r/id.html
# Count Usage: https://www.terraform.io/intro/examples/count.html

# *************************************************************
# Terraform state artifactory backup.
# https://www.terraform.io/docs/backends/types/artifactory.html
# *************************************************************
terraform {
  backend "http" {
  }
}
# *************************************************************
# BCT: bct-x, check CONTRIBUTING.md before editing here.
# Labels bct_statebuild and bct_poolbuild are used for bct when we have
# vms with bct code server and bct data server
# *************************************************************

locals {
  module_settings = {
    # EXECUTOR NODES
    "bct_ch1_ps02_rel_executor_node_001" = { datastore = "BPS02-A3_DRE-BUILD-VMS-01", vm_count = "1", labels = "ps02 executor_agent", cpu_core = "5", ram_count = 16384 }

    # SMOKE TEST NODE
    "bct_ch1_ps02_rel_smoke_test_001" = { datastore = "BPS02-A1_DRE-BUILD-VMS-02", vm_count = "1", labels = "ps smoke", cpu_core = "5" }

    # STATEBUILD NODES
    "bct_ch1_ps02_pool_state_bct_ch1_win64_001"   = { datastore = "BPS02-A1_DRE-BUILD-VMS-03", vm_count = "3", labels = "ps statebuild poolbuild win64" }
    "bct_ch1_ps02_pool_state_bct_ch1_win64_003"   = { datastore = "BPS02-A3_DRE-BUILD-VMS-02", vm_count = "3", labels = "ps statebuild poolbuild win64" }
    "bct_ch1_ps02_pool_state_bct_ch1_ps5_001"     = { datastore = "BPS02-A1_DRE-BUILD-VMS-03", vm_count = "3", labels = "ps statebuild poolbuild ps5" }
    "bct_ch1_ps02_pool_state_bct_ch1_ps5_003"     = { datastore = "BPS02-A3_DRE-BUILD-VMS-02", vm_count = "3", labels = "ps statebuild poolbuild ps5" }
    "bct_ch1_ps02_pool_state_bct_ch1_xbsx_001"    = { datastore = "BPS02-A1_DRE-BUILD-VMS-03", vm_count = "3", labels = "ps statebuild poolbuild xbsx" }
    "bct_ch1_ps02_pool_state_bct_ch1_xbsx_002"    = { datastore = "BPS02-A3_DRE-BUILD-VMS-01", vm_count = "3", labels = "ps statebuild poolbuild xbsx" }
    "bct_ch1_ps02_pool_state_bct_ch1_linux64_001" = { datastore = "BPS02-A1_DRE-BUILD-VMS-03", vm_count = "1", labels = "ps statebuild poolbuild linux64" }
    "bct_ch1_ps02_pool_state_bct_ch1_linux64_002" = { datastore = "BPS02-A3_DRE-BUILD-VMS-01", vm_count = "1", labels = "ps statebuild poolbuild linux64" }
    "bct_ch1_ps02_pool_state_bct_ch1_servers_001" = { datastore = "BPS02-A1_DRE-BUILD-VMS-03", vm_count = "1", labels = "ps statebuild poolbuild server linuxserver" }
    "bct_ch1_ps02_pool_state_bct_ch1_servers_002" = { datastore = "BPS02-A3_DRE-BUILD-VMS-01", vm_count = "1", labels = "ps statebuild poolbuild server linuxserver" }

    # CH1-STAGE CODE
    "ps_ch1_stage_code_linux64_final_001"         = { datastore = "BPS02-A4_DRE-BUILD-VMS-01", vm_count = "1", labels = "ps02 CH1-stage code linux64 final" }
    "ps_ch1_stage_code_linuxserver_final_001"     = { datastore = "BPS02-A4_DRE-BUILD-VMS-01", vm_count = "1", labels = "ps02 CH1-stage code linux64server final" }
    "ps_ch1_stage_code_ps5_final_001"             = { datastore = "BPS02-A4_DRE-BUILD-VMS-01", vm_count = "1", labels = "ps02 CH1-stage code ps5 final" }
    "ps_ch1_stage_code_ps5_performance_001"       = { datastore = "BPS02-A4_DRE-BUILD-VMS-01", vm_count = "1", labels = "ps02 CH1-stage code ps5 performance" }
    "ps_ch1_stage_code_ps5_release_001"           = { datastore = "BPS02-A4_DRE-BUILD-VMS-01", vm_count = "1", labels = "ps02 CH1-stage code ps5 release" }
    "ps_ch1_stage_code_ps5_retail_001"            = { datastore = "BPS02-A4_DRE-BUILD-VMS-01", vm_count = "1", labels = "ps02 CH1-stage code ps5 retail" }
    "ps_ch1_stage_code_tool_release_02"           = { datastore = "BPS02-A4_DRE-BUILD-VMS-01", vm_count = "1", labels = "ps02 CH1-stage code tool release" }
    "ps_ch1_stage_code_win64game_final_001"       = { datastore = "BPS02-A4_DRE-BUILD-VMS-01", vm_count = "1", labels = "ps02 CH1-stage code win64game final" }
    "ps_ch1_stage_code_win64game_performance_001" = { datastore = "BPS02-A4_DRE-BUILD-VMS-01", vm_count = "1", labels = "ps02 CH1-stage code win64game performance" }
    "ps_ch1_stage_code_win64game_release_001"     = { datastore = "BPS02-A4_DRE-BUILD-VMS-01", vm_count = "1", labels = "ps02 CH1-stage code win64game release" }
    "ps_ch1_stage_code_win64game_retail_001"      = { datastore = "BPS02-A4_DRE-BUILD-VMS-01", vm_count = "1", labels = "ps02 CH1-stage code win64game retail" }
    "ps_ch1_stage_code_win64server_final_001"     = { datastore = "BPS02-A4_DRE-BUILD-VMS-01", vm_count = "1", labels = "ps02 CH1-stage code win64server final" }
    "ps_ch1_stage_code_win64server_release_001"   = { datastore = "BPS02-A4_DRE-BUILD-VMS-03", vm_count = "1", labels = "ps02 CH1-stage code win64server release" }
    "ps_ch1_stage_code_xbsx_final_001"            = { datastore = "BPS02-A4_DRE-BUILD-VMS-01", vm_count = "1", labels = "ps02 CH1-stage code xbsx final" }
    "ps_ch1_stage_code_xbsx_performance_001"      = { datastore = "BPS02-A4_DRE-BUILD-VMS-01", vm_count = "1", labels = "ps02 CH1-stage code xbsx performance" }
    "ps_ch1_stage_code_xbsx_release_001"          = { datastore = "BPS02-A4_DRE-BUILD-VMS-01", vm_count = "1", labels = "ps02 CH1-stage code xbsx release" }
    "ps_ch1_stage_code_xbsx_retail_001"           = { datastore = "BPS02-A4_DRE-BUILD-VMS-01", vm_count = "1", labels = "ps02 CH1-stage code xbsx retail" }

    # CH1-STAGE DATA/PATCHDATA
    "ps_ch1_stage_data_webexport_001"  = { datastore = "BPS02-A3_DRE-BUILD-VMS-03", vm_count = "1", labels = "ps02 CH1-stage data webexport" }
    "ps_ch1_stage_patchdata_ps5_001"   = { datastore = "BPS02-A4_DRE-BUILD-VMS-01", vm_count = "1", labels = "ps02 CH1-stage patchdata ps5" }
    "ps_ch1_stage_patchdata_ps5_002"   = { datastore = "BPS02-A3_DRE-BUILD-VMS-02", vm_count = "1", labels = "ps02 CH1-stage patchdata ps5" }
    "ps_ch1_stage_patchdata_win64_001" = { datastore = "BPS02-A4_DRE-BUILD-VMS-01", vm_count = "1", labels = "ps02 CH1-stage patchdata win64" }
    "ps_ch1_stage_patchdata_win64_002" = { datastore = "BPS02-A3_DRE-BUILD-VMS-02", vm_count = "1", labels = "ps02 CH1-stage patchdata win64" }
    "ps_ch1_stage_patchdata_xbsx_001"  = { datastore = "BPS02-A4_DRE-BUILD-VMS-01", vm_count = "1", labels = "ps02 CH1-stage patchdata xbsx" }
    "ps_ch1_stage_patchdata_xbsx_002"  = { datastore = "BPS02-A3_DRE-BUILD-VMS-02", vm_count = "1", labels = "ps02 CH1-stage patchdata xbsx" }

    # CH1-STAGE FROSTY
    "ps_ch1_stage_frosty_linux64_final_files_001"     = { datastore = "BPS02-A3_DRE-BUILD-VMS-03", vm_count = "1", labels = "ps02 CH1-stage frosty linux64 final files" }
    "ps_ch1_stage_frosty_server_final_files_001"      = { datastore = "BPS02-A2_DRE-BUILD-VMS-03", vm_count = "1", labels = "ps02 CH1-stage frosty server final files" }
    "ps_ch1_stage_frosty_ps5_final_files_001"         = { datastore = "BPS02-A2_DRE-BUILD-VMS-03", vm_count = "1", labels = "ps02 CH1-stage frosty ps5 final files" }
    "ps_ch1_stage_frosty_ps5_performance_files_001"   = { datastore = "BPS02-A2_DRE-BUILD-VMS-03", vm_count = "1", labels = "ps02 CH1-stage frosty ps5 performance files" }
    "ps_ch1_stage_frosty_win64_final_files_001"       = { datastore = "BPS02-A2_DRE-BUILD-VMS-03", vm_count = "1", labels = "ps02 CH1-stage frosty win64 final files" }
    "ps_ch1_stage_frosty_win64_performance_files_001" = { datastore = "BPS02-A2_DRE-BUILD-VMS-03", vm_count = "1", labels = "ps02 CH1-stage frosty win64 performance files" }
    "ps_ch1_stage_frosty_xbsx_final_files_001"        = { datastore = "BPS02-A2_DRE-BUILD-VMS-03", vm_count = "1", labels = "ps02 CH1-stage frosty xbsx final files" }
    "ps_ch1_stage_frosty_xbsx_performance_files_001"  = { datastore = "BPS02-A2_DRE-BUILD-VMS-03", vm_count = "1", labels = "ps02 CH1-stage frosty xbsx performance files" }

    # Temp set to zero for clean terraform cleanup
    "ps_ch1_stage_frosty_linuxserver_final_digital_001" = { datastore = "BPS02-A3_DRE-BUILD-VMS-03", vm_count = "1", labels = "ps02 CH1-stage frosty linuxserver final digital" }
    "ps_ch1_stage_frosty_ps5_final_digital_001"         = { datastore = "BPS02-A2_DRE-BUILD-VMS-03", vm_count = "0", labels = "ps02 CH1-stage frosty ps5 final digital" }
    "ps_ch1_stage_frosty_ps5_retail_digital_001"        = { datastore = "BPS02-A2_DRE-BUILD-VMS-03", vm_count = "0", labels = "ps02 CH1-stage frosty ps5 retail digital" }
    "ps_ch1_stage_frosty_win64_final_digital_001"       = { datastore = "BPS02-A2_DRE-BUILD-VMS-03", vm_count = "0", labels = "ps02 CH1-stage frosty win64 final digital" }
    "ps_ch1_stage_frosty_win64_release_digital_001"     = { datastore = "BPS02-A2_DRE-BUILD-VMS-03", vm_count = "0", labels = "ps02 CH1-stage frosty win64 release digital" }
    "ps_ch1_stage_frosty_win64_retail_digital_001"      = { datastore = "BPS02-A2_DRE-BUILD-VMS-03", vm_count = "0", labels = "ps02 CH1-stage frosty win64 retail digital" }
    "ps_ch1_stage_frosty_win64_retail_steam_001"        = { datastore = "BPS02-A2_DRE-BUILD-VMS-03", vm_count = "0", labels = "ps02 CH1-stage frosty win64 retail steam_build" }
    "ch1_stage_steam_build_001"                         = { datastore = "BPS02-A3_DRE-BUILD-VMS-01", vm_count = "0", labels = "ps02 CH1-stage frosty win64 final steam_build" }
    "ps_ch1_stage_frosty_xbsx_final_digital_001"        = { datastore = "BPS02-A2_DRE-BUILD-VMS-03", vm_count = "0", labels = "ps02 CH1-stage frosty xbsx final digital" }
    "ps_ch1_stage_frosty_xbsx_retail_digital_001"       = { datastore = "BPS02-A2_DRE-BUILD-VMS-03", vm_count = "0", labels = "ps02 CH1-stage frosty xbsx retail digital" }
    "ps02_ch1_stage_frosty_win64_final_combine_001"     = { datastore = "BPS02-A1_DRE-BUILD-VMS-01", vm_count = "0", labels = "ps02 CH1-stage frosty win64 final combine" }
    "ps02_ch1_stage_frosty_win64_retail_combine_001"    = { datastore = "BPS02-A4_DRE-BUILD-VMS-01", vm_count = "0", labels = "ps02 CH1-stage frosty win64 retail combine" }
    "ps02_ch1_stage_frosty_ps5_final_combine_002"       = { datastore = "BPS02-A2_DRE-BUILD-VMS-01", vm_count = "0", labels = "ps02 CH1-stage frosty ps5 final combine 128GB 40core", ram_count = 131072, cpu_core = "40" }
    "ps02_ch1_stage_frosty_ps5_retail_combine_002"      = { datastore = "BPS02-A2_DRE-BUILD-VMS-03", vm_count = "0", labels = "ps02 CH1-stage frosty ps5 retail combine 128GB 40core", ram_count = 131072, cpu_core = "40" }
    "ps02_ch1_stage_frosty_xbsx_final_combine_001"      = { datastore = "BPS02-A1_DRE-BUILD-VMS-01", vm_count = "0", labels = "ps02 CH1-stage frosty xbsx final combine" }
    "ps02_ch1_stage_frosty_xbsx_retail_combine_001"     = { datastore = "BPS02-A4_DRE-BUILD-VMS-01", vm_count = "0", labels = "ps02 CH1-stage frosty xbsx retail combine" }

    # CH1-stage PATCHFROSTY
    "ps_ch1_stage_patchfrosty_linuxserver_final_digital_001" = { datastore = "BPS02-A3_DRE-BUILD-VMS-03", vm_count = "1", labels = "ps02 CH1-stage patchfrosty linuxserver final digital" }
    "ps_ch1_stage_patchfrosty_ps5_final_digital_001"         = { datastore = "BPS02-A2_DRE-BUILD-VMS-03", vm_count = "1", labels = "ps02 CH1-stage patchfrosty ps5 final digital" }
    "ps_ch1_stage_patchfrosty_ps5_retail_digital_001"        = { datastore = "BPS02-A2_DRE-BUILD-VMS-03", vm_count = "1", labels = "ps02 CH1-stage patchfrosty ps5 retail digital" }
    "ps_ch1_stage_patchfrosty_win64_final_digital_001"       = { datastore = "BPS02-A2_DRE-BUILD-VMS-03", vm_count = "1", labels = "ps02 CH1-stage patchfrosty win64 final digital" }
    "ps_ch1_stage_patchfrosty_win64_release_digital_001"     = { datastore = "BPS02-A2_DRE-BUILD-VMS-03", vm_count = "1", labels = "ps02 CH1-stage patchfrosty win64 release digital" }
    "ps_ch1_stage_patchfrosty_win64_retail_digital_001"      = { datastore = "BPS02-A2_DRE-BUILD-VMS-03", vm_count = "1", labels = "ps02 CH1-stage patchfrosty win64 retail digital" }
    "ps_ch1_stage_patchfrosty_win64_retail_steam_001"        = { datastore = "BPS02-A2_DRE-BUILD-VMS-03", vm_count = "1", labels = "ps02 CH1-stage patchfrosty win64 retail steam_build" }
    "ps_ch1_stage_patchfrosty_win64_final_steam_001"         = { datastore = "BPS02-A3_DRE-BUILD-VMS-01", vm_count = "1", labels = "ps02 CH1-stage patchfrosty win64 final steam_build" }
    "ps_ch1_stage_patchfrosty_xbsx_final_digital_001"        = { datastore = "BPS02-A2_DRE-BUILD-VMS-03", vm_count = "1", labels = "ps02 CH1-stage patchfrosty xbsx final digital" }
    "ps_ch1_stage_patchfrosty_xbsx_retail_digital_001"       = { datastore = "BPS02-A2_DRE-BUILD-VMS-03", vm_count = "1", labels = "ps02 CH1-stage patchfrosty xbsx retail digital" }

    # CH1-stage COMBINE PATCHFROSTY
    "ps02_ch1_stage_patchfrosty_win64_final_combine_001"  = { datastore = "BPS02-A1_DRE-BUILD-VMS-01", vm_count = "1", labels = "ps02 CH1-stage patchfrosty win64 final combine" }
    "ps02_ch1_stage_patchfrosty_win64_retail_combine_001" = { datastore = "BPS02-A4_DRE-BUILD-VMS-01", vm_count = "1", labels = "ps02 CH1-stage patchfrosty win64 retail combine" }
    "ps02_ch1_stage_patchfrosty_ps5_final_combine_002"    = { datastore = "BPS02-A2_DRE-BUILD-VMS-01", vm_count = "1", labels = "ps02 CH1-stage patchfrosty ps5 final combine 128GB 40core", ram_count = 131072, cpu_core = "40" }
    "ps02_ch1_stage_patchfrosty_ps5_retail_combine_002"   = { datastore = "BPS02-A2_DRE-BUILD-VMS-03", vm_count = "1", labels = "ps02 CH1-stage patchfrosty ps5 retail combine 128GB 40core", ram_count = 131072, cpu_core = "40" }
    "ps02_ch1_stage_patchfrosty_xbsx_final_combine_001"   = { datastore = "BPS02-A1_DRE-BUILD-VMS-01", vm_count = "1", labels = "ps02 CH1-stage patchfrosty xbsx final combine" }
    "ps02_ch1_stage_patchfrosty_xbsx_retail_combine_001"  = { datastore = "BPS02-A4_DRE-BUILD-VMS-01", vm_count = "1", labels = "ps02 CH1-stage patchfrosty xbsx retail combine" }

    # CH1-BFLABS-STAGE CODE
    "ps02_ch1_bflabs_stage_code_linux64_final_001"         = { datastore = "BPS02-A4_DRE-BUILD-VMS-03", vm_count = "1", labels = "ps02 CH1-bflabs-stage code linux64 final" }
    "ps02_ch1_bflabs_stage_code_linuxserver_final_001"     = { datastore = "BPS02-A4_DRE-BUILD-VMS-03", vm_count = "1", labels = "ps02 CH1-bflabs-stage code linux64server final" }
    "ps02_ch1_bflabs_stage_code_ps5_final_001"             = { datastore = "BPS02-A4_DRE-BUILD-VMS-03", vm_count = "1", labels = "ps02 CH1-bflabs-stage code ps5 final" }
    "ps02_ch1_bflabs_stage_code_ps5_performance_001"       = { datastore = "BPS02-A4_DRE-BUILD-VMS-03", vm_count = "1", labels = "ps02 CH1-bflabs-stage code ps5 performance" }
    "ps02_ch1_bflabs_stage_code_ps5_release_001"           = { datastore = "BPS02-A4_DRE-BUILD-VMS-03", vm_count = "1", labels = "ps02 CH1-bflabs-stage code ps5 release" }
    "ps02_ch1_bflabs_stage_code_ps5_retail_001"            = { datastore = "BPS02-A4_DRE-BUILD-VMS-03", vm_count = "1", labels = "ps02 CH1-bflabs-stage code ps5 retail" }
    "ps02_ch1_bflabs_stage_code_tool_release_001"          = { datastore = "BPS02-A4_DRE-BUILD-VMS-03", vm_count = "1", labels = "ps02 CH1-bflabs-stage code tool release" }
    "ps02_ch1_bflabs_stage_code_win64game_final_001"       = { datastore = "BPS02-A4_DRE-BUILD-VMS-03", vm_count = "1", labels = "ps02 CH1-bflabs-stage code win64game final" }
    "ps02_ch1_bflabs_stage_code_win64game_performance_001" = { datastore = "BPS02-A4_DRE-BUILD-VMS-03", vm_count = "1", labels = "ps02 CH1-bflabs-stage code win64game performance" }
    "ps02_ch1_bflabs_stage_code_win64game_release_001"     = { datastore = "BPS02-A4_DRE-BUILD-VMS-03", vm_count = "1", labels = "ps02 CH1-bflabs-stage code win64game release" }
    "ps02_ch1_bflabs_stage_code_win64game_retail_001"      = { datastore = "BPS02-A4_DRE-BUILD-VMS-03", vm_count = "1", labels = "ps02 CH1-bflabs-stage code win64game retail" }
    "ps02_ch1_bflabs_stage_code_win64server_final_001"     = { datastore = "BPS02-A4_DRE-BUILD-VMS-03", vm_count = "1", labels = "ps02 CH1-bflabs-stage code win64server final" }
    "ps02_ch1_bflabs_stage_code_xbsx_final_001"            = { datastore = "BPS02-A4_DRE-BUILD-VMS-03", vm_count = "1", labels = "ps02 CH1-bflabs-stage code xbsx final" }
    "ps02_ch1_bflabs_stage_code_xbsx_performance_001"      = { datastore = "BPS02-A4_DRE-BUILD-VMS-03", vm_count = "1", labels = "ps02 CH1-bflabs-stage code xbsx performance" }
    "ps02_ch1_bflabs_stage_code_xbsx_release_001"          = { datastore = "BPS02-A4_DRE-BUILD-VMS-03", vm_count = "1", labels = "ps02 CH1-bflabs-stage code xbsx release" }
    "ps02_ch1_bflabs_stage_code_xbsx_retail_001"           = { datastore = "BPS02-A4_DRE-BUILD-VMS-03", vm_count = "1", labels = "ps02 CH1-bflabs-stage code xbsx retail" }

    # CH1-BFLABS-STAGE DATA/PATCHDATA
    "ps02_ch1_bflabs_stage_data_webexport_001"  = { datastore = "BPS02-A4_DRE-BUILD-VMS-03", vm_count = "1", labels = "ps02 CH1-bflabs-stage data webexport" }
    "ps02_ch1_bflabs_stage_patchdata_ps5_001"   = { datastore = "BPS02-A4_DRE-BUILD-VMS-03", vm_count = "1", labels = "ps02 CH1-bflabs-stage patchdata ps5" }
    "ps02_ch1_bflabs_stage_patchdata_ps5_002"   = { datastore = "BPS02-A2_DRE-BUILD-VMS-03", vm_count = "1", labels = "ps02 CH1-bflabs-stage patchdata ps5" }
    "ps02_ch1_bflabs_stage_patchdata_win64_001" = { datastore = "BPS02-A4_DRE-BUILD-VMS-03", vm_count = "1", labels = "ps02 CH1-bflabs-stage patchdata win64" }
    "ps02_ch1_bflabs_stage_patchdata_win64_002" = { datastore = "BPS02-A2_DRE-BUILD-VMS-03", vm_count = "1", labels = "ps02 CH1-bflabs-stage patchdata win64" }
    "ps02_ch1_bflabs_stage_patchdata_xbsx_001"  = { datastore = "BPS02-A4_DRE-BUILD-VMS-03", vm_count = "1", labels = "ps02 CH1-bflabs-stage patchdata xbsx" }
    "ps02_ch1_bflabs_stage_patchdata_xbsx_002"  = { datastore = "BPS02-A2_DRE-BUILD-VMS-03", vm_count = "1", labels = "ps02 CH1-bflabs-stage patchdata xbsx" }

    # CH1-BFLABS-STAGE FROSTY
    "ps02_ch1_bflabs_stage_frosty_linux64_final_files_001"  = { datastore = "BPS02-A4_DRE-BUILD-VMS-03", vm_count = "1", labels = "ps02 CH1-bflabs-stage frosty linux64 final files" }
    "ps02_ch1_bflabs_stage_frosty_linser_final_digital_001" = { datastore = "BPS02-A4_DRE-BUILD-VMS-03", vm_count = "1", labels = "ps02 CH1-bflabs-stage frosty linuxserver final digital" }
    "ps02_ch1_bflabs_stage_frosty_server_final_files_001"   = { datastore = "BPS02-A4_DRE-BUILD-VMS-03", vm_count = "1", labels = "ps02 CH1-bflabs-stage frosty server final files" }
    "ps02_ch1_bflabs_stage_frosty_server_final_digital_001" = { datastore = "BPS02-A4_DRE-BUILD-VMS-03", vm_count = "1", labels = "ps02 CH1-bflabs-stage frosty server final digital" }
    "ps02_ch1_bflabs_stage_frosty_ps5_final_files_001"      = { datastore = "BPS02-A4_DRE-BUILD-VMS-03", vm_count = "1", labels = "ps02 CH1-bflabs-stage frosty ps5 final files" }
    "ps02_ch1_bflabs_stage_frosty_win64_final_files_001"    = { datastore = "BPS02-A4_DRE-BUILD-VMS-03", vm_count = "1", labels = "ps02 CH1-bflabs-stage frosty win64 final files" }
    "ps02_ch1_bflabs_stage_frosty_xbsx_final_files_001"     = { datastore = "BPS02-A4_DRE-BUILD-VMS-03", vm_count = "1", labels = "ps02 CH1-bflabs-stage frosty xbsx final files" }

    # CH1-BFLABS-STAGE PATCHFROSTY
    "ps02_ch1_bflabs_stage_patchfrosty_ps5_final_digital_001"     = { datastore = "BPS02-A4_DRE-BUILD-VMS-03", vm_count = "1", labels = "ps02 CH1-bflabs-stage patchfrosty ps5 final digital" }
    "ps02_ch1_bflabs_stage_patchfrosty_ps5_release_digital_001"   = { datastore = "BPS02-A4_DRE-BUILD-VMS-03", vm_count = "1", labels = "ps02 CH1-bflabs-stage patchfrosty ps5 retail digital" }
    "ps02_ch1_bflabs_stage_patchfrosty_win64_final_digital_001"   = { datastore = "BPS02-A4_DRE-BUILD-VMS-03", vm_count = "1", labels = "ps02 CH1-bflabs-stage patchfrosty win64 final digital" }
    "ps02_ch1_bflabs_stage_patchfrosty_win64_release_digital_001" = { datastore = "BPS02-A4_DRE-BUILD-VMS-03", vm_count = "1", labels = "ps02 CH1-bflabs-stage patchfrosty win64 retail digital" }
    "ps02_ch1_bflabs_stage_patchfrosty_xbsx_final_digital_001"    = { datastore = "BPS02-A4_DRE-BUILD-VMS-03", vm_count = "1", labels = "ps02 CH1-bflabs-stage patchfrosty xbsx final digital" }
    "ps02_ch1_bflabs_stage_patchfrosty_xbsx_release_digital_001"  = { datastore = "BPS02-A4_DRE-BUILD-VMS-03", vm_count = "1", labels = "ps02 CH1-bflabs-stage patchfrosty xbsx retail digital" }
    "ps02_ch1_bflabs_stage_patchfrosty_ps5_final_combine_001"     = { datastore = "BPS02-A4_DRE-BUILD-VMS-01", vm_count = "1", labels = "ps02 CH1-bflabs-stage patchfrosty ps5 final combine" }
    "ps02_ch1_bflabs_stage_patchfrosty_win64_final_combine_001"   = { datastore = "BPS02-A4_DRE-BUILD-VMS-01", vm_count = "1", labels = "ps02 CH1-bflabs-stage patchfrosty win64 final combine" }
    "ps02_ch1_bflabs_stage_patchfrosty_xbsx_final_combine_001"    = { datastore = "BPS02-A4_DRE-BUILD-VMS-02", vm_count = "1", labels = "ps02 CH1-bflabs-stage patchfrosty xbsx final combine" }
    "ps02_ch1_bflabs_stage_patchfrosty_ps5_retail_combine_001"    = { datastore = "BPS02-A4_DRE-BUILD-VMS-01", vm_count = "1", labels = "ps02 CH1-bflabs-stage patchfrosty ps5 retail combine" }
    "ps02_ch1_bflabs_stage_patchfrosty_win64_retail_combine_001"  = { datastore = "BPS02-A4_DRE-BUILD-VMS-01", vm_count = "1", labels = "ps02 CH1-bflabs-stage patchfrosty win64 retail combine" }
    "ps02_ch1_bflabs_stage_patchfrosty_xbsx_retail_combine_001"   = { datastore = "BPS02-A4_DRE-BUILD-VMS-02", vm_count = "1", labels = "ps02 CH1-bflabs-stage patchfrosty xbsx retail combine" }

    # Steam Build
    "ch1_bflabs_stage_steam_build_final_001"  = { datastore = "BPS02-A3_DRE-BUILD-VMS-01", vm_count = "1", labels = "win64 CH1-bflabs-stage final steam_build frosty" }
    "ch1_bflabs_stage_steam_build_retail_001" = { datastore = "BPS02-A3_DRE-BUILD-VMS-01", vm_count = "1", labels = "win64 CH1-bflabs-stage retail steam_build frosty" }

    # Utilities
    "ps02_ch1_copy_build_to_azure_001" = { datastore = "BPS02-A4_DRE-BUILD-VMS-01", vm_count = "1", labels = "ps02 copy_build_to_azure" }

    # CH1-RELEASE INTEGRATIONS
    "bct_ch1_ps02_pool_state_bct_ch1_integrations_001" = { datastore = "BPS02-A3_DRE-BUILD-VMS-02", vm_count = "1", labels = "CH1-release_to_CH1-SP-release" }
  }
}

module "dynamic_local_module_primary" {
  source   = "../../modules/windows_attach_module_v3.5"
  for_each = local.module_settings

  vsphere_datastore       = each.value.datastore
  vm_count                = each.value.vm_count
  vm_prefix               = try(each.value.vm_prefix, "bct2-")
  jenkins_slave_labels    = each.value.labels
  role                    = try(each.value.role, "https://bct-ch1-rel-jenkins.cobra.dre.ea.com")
  vsphere_compute_cluster = try(each.value.compute_cluster, "DICE-BUILD")
  ram_count               = try(each.value.ram_count, 65536)
  jenkins_websocket       = try(each.value.jenkins_websocket, "disabled")

  vsphere_template      = var.packer_template
  vsphere_network       = var.bct_network
  vsphere_datacenter    = var.bct_datacenter
  vsphere_folder        = "DICE/dre-terraform-nodes/bct_nodes"
  domain_admin          = var.domain_admin
  domain_admin_password = var.domain_password
  local_admin_user      = var.local_username
  local_admin_password  = var.local_password
  project_dir           = var.project_dir
  project_name          = var.project_name
  commit_sha            = var.commit_sha
  commit_user           = var.commit_user
  commit_url            = var.commit_url
  disk_size             = var.disk_size
  domain_name           = "dice.ad.ea.com"
  domain_ou             = "OU=BuildMonkeys,OU=Computers,OU=Stockholm,OU=Offices,DC=dice,DC=ad,DC=ea,DC=com"
  hardware_version      = var.hardware_version
  local_admin_group     = var.local_admin_group
}

# *************************************************************
#  Setting up the dynamic output needed for downstream pipelines
# *************************************************************
# Notes:
# For Expressions: https://www.terraform.io/docs/language/expressions/for.html
# Key Functions: https://www.terraform.io/docs/language/functions/keys.html
# flatten Function: https://www.terraform.io/docs/language/functions/flatten.html
# Output Values: https://www.terraform.io/docs/language/values/outputs.html
# Local Values: https://www.terraform.io/docs/language/values/locals.html
#
# The solution outputs the same way as previously in output.tf
# example:
# node_name_uuids = [
#  {
#   "id"   = "JF5D"
#   "name" = "245e43"
#   },
#
# *************************************************************
#  Dynamic Output, check CONTRIBUTING.md before editing here.
# *************************************************************
locals {
  nodes_output = flatten([
    for mod in keys(local.module_settings) : [
      for node in module.dynamic_local_module_primary[mod].nodes : [
        {
          name              = node.name
          id                = node.id
          custom_attributes = node.custom_attributes
        }
      ]
    ]
  ])
  node_name_uuids_output = flatten([
    for mod in keys(local.module_settings) : [
      for node in module.dynamic_local_module_primary[mod].node_name_uuids : [
        {
          name = node.hex
          id   = node.id
        }
      ]
    ]
  ])
}

output "nodes" {
  value = local.nodes_output
}

output "node_name_uuids" {
  value = local.node_name_uuids_output
}
