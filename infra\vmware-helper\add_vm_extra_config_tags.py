#!/usr/bin/env python
# Copyright 2014 <PERSON>
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
# http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""
Sample for adding extra config tags to a VM
"""

import requests
from pyVmomi import vim
from tools import cli, service_instance, pchelper, tasks

requests.packages.urllib3.disable_warnings(
    requests.packages.urllib3.exceptions.InsecureRequestWarning)

parser = cli.Parser()
args = parser.get_args()
si = service_instance.connect(args)

spec = vim.vm.ConfigSpec()
opt = vim.option.OptionValue()
spec.extraConfig = []

options_values = {
    "jenkins_slave_labels": "statebuild kin_autotest_6 poolbuild linux64 server linuxserver"
}

vm_names  = {
  "rke-worker-003"
}
for vm_name in vm_names:
    print("Start updating setting for VM. {0}".format(vm_name) )
    vm = pchelper.get_obj(si.RetrieveContent(), [vim.VirtualMachine], vm_name)
    if not vm:
        raise SystemExit("Unable to locate VirtualMachine.")

    for k, v in options_values.items():
        opt.key = k
        opt.value = v
        spec.extraConfig.append(opt)
        opt = vim.option.OptionValue()

    task = vm.ReconfigVM_Task(spec)
    tasks.wait_for_tasks(si, [task])
    print(" --- setting updated for {0} ---".format(vm_name))
print("done.")
