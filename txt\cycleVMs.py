#!/usr/bin/env python3

"""
Power cycle all VMs in a specific datacenter, sequentially, with throttling.

- Connects to vCenter.
- Finds the specified datacenter and enumerates all VMs under its vmFolder.
- For each VM (skipping templates):
  - If powered on: power off and wait for task to complete.
  - Sleep for the configured throttle seconds.
  - Power on and wait for task to complete.
  - Sleep again for the configured throttle seconds before proceeding to next VM.

Note: This performs a full vSphere power off/on (not a guest reboot).
"""

import sys
import time
from typing import Dict, List
from pathlib import Path
import argparse
import atexit
import ssl

from pyVim import connect
from pyVmomi import vim, vmodl


def parse_vsphere_vars_file(file_path: str) -> Dict[str, str]:
    """Parse key-value pairs from vsphere_variable.txt.

    Lines use the format: "key value" separated by whitespace. Blank lines and
    lines starting with '#' are ignored. If a value contains spaces, the text
    after the first whitespace will be treated as the value.
    """
    data: Dict[str, str] = {}
    with open(file_path, "r", encoding="utf-8") as fh:
        for raw in fh:
            line = raw.strip()
            if not line or line.startswith("#"):
                continue
            parts = line.split()
            if len(parts) < 2:
                continue
            key = parts[0].strip()
            value = " ".join(parts[1:]).strip()
            data[key] = value
    return data


def inject_vsphere_vars_into_argv(file_path: str | None = None) -> None:
    """Inject missing CLI flags from txt/vsphere_variable.txt into sys.argv.

    This ensures required arguments are satisfied by populating -s/--host,
    -u/--user, -p/--password, and --datacenter-name (or -d) before parsing.
    Explicit command-line values remain authoritative and will not be
    overridden.
    """
    try:
        base = Path(__file__).resolve().parent.parent
        cfg_path = Path(file_path) if file_path else base / "txt" / "vsphere_variable.txt"
        if not cfg_path.exists():
            return
        kv = parse_vsphere_vars_file(str(cfg_path))

        def has_any_flag(flags: List[str]) -> bool:
            argv = sys.argv
            return any(flag in argv for flag in flags)

        new_args: List[str] = []
        if "host" in kv and not has_any_flag(["-s", "--host"]):
            new_args.extend(["-s", kv["host"]])
        if "user" in kv and not has_any_flag(["-u", "--user"]):
            new_args.extend(["-u", kv["user"]])
        if "password" in kv and not has_any_flag(["-p", "--password"]):
            new_args.extend(["-p", kv["password"]])
        # Prefer long form for clarity; also accept -d
        if "data-center-name" in kv and not has_any_flag(["-d", "--datacenter-name"]):
            new_args.extend(["--datacenter-name", kv["data-center-name"]])

        if new_args:
            sys.argv.extend(new_args)
    except Exception as ex:
        print("Warning: failed to apply vSphere variables to argv: {0}".format(ex))


# -----------------------------
# Minimal local CLI and helpers
# -----------------------------

def build_arg_parser() -> argparse.ArgumentParser:
    """Build the argument parser (self-contained, no external tools.cli)."""
    parser = argparse.ArgumentParser(
        description=(
            "Power cycle all VMs in a vSphere datacenter sequentially with throttling."
        )
    )
    parser.add_argument(
        "-s",
        "--host",
        dest="host",
        required=False,
        help="vCenter server hostname or IP",
    )
    parser.add_argument(
        "-u",
        "--user",
        dest="user",
        required=False,
        help="vCenter username",
    )
    parser.add_argument(
        "-p",
        "--password",
        dest="password",
        required=False,
        help="vCenter password",
    )
    parser.add_argument(
        "-d",
        "--datacenter-name",
        dest="datacenter_name",
        required=True,
        help="Target datacenter name",
    )
    parser.add_argument(
        "--sleep-seconds",
        type=int,
        default=180,
        help="Throttle delay in seconds between operations (default: 180)",
    )
    parser.add_argument(
        "--dry-run",
        action="store_true",
        help="List targeted VMs without changing power state",
    )
    parser.add_argument(
        "--skip-if-off",
        action="store_true",
        help="If VM is already powered off, skip power on cycle for that VM",
    )
    parser.add_argument(
        "--disable-ssl-verification",
        action="store_true",
        help="Disable SSL certificate verification when connecting to vCenter",
    )
    return parser


def connect_vcenter(host: str, user: str, password: str, disable_ssl_verification: bool = True):
    """Connect to vCenter and return a ServiceInstance.

    Uses SmartConnect with optional SSL verification disabled (default True).
    Automatically registers Disconnect on interpreter exit.
    """
    if not host or not user or not password:
        raise SystemExit(
            "Missing vCenter connection parameters (host/user/password)."
        )

    si = None
    try:
        if disable_ssl_verification:
            ctx = ssl.create_default_context()
            ctx.check_hostname = False
            ctx.verify_mode = ssl.CERT_NONE
            si = connect.SmartConnect(host=host, user=user, pwd=password, sslContext=ctx)
        else:
            si = connect.SmartConnect(host=host, user=user, pwd=password)
    except Exception as ex:
        raise SystemExit("Failed to connect to vCenter {0}: {1}".format(host, ex))

    if not si:
        raise SystemExit("Could not obtain ServiceInstance from vCenter {0}".format(host))

    atexit.register(connect.Disconnect, si)
    return si


def wait_for_tasks(tasks_list: List[vim.Task]) -> None:
    """Wait for vSphere tasks to complete (success or error)."""
    remaining = list(tasks_list)
    while remaining:
        next_round: List[vim.Task] = []
        for task in remaining:
            info = task.info
            state = info.state
            if state == vim.TaskInfo.State.success:
                continue
            if state == vim.TaskInfo.State.error:
                msg = getattr(info.error, 'msg', str(info.error))
                raise RuntimeError("Task failed: {0}".format(msg))
            # Still running
            next_round.append(task)
        remaining = next_round
        if remaining:
            time.sleep(1)


def get_obj(content, vimtypes: List[type], name: str):
    """Return the first object matching name among given types, else raise."""
    view = content.viewManager.CreateContainerView(content.rootFolder, vimtypes, True)
    try:
        for obj in view.view:
            obj_name = getattr(obj, 'name', None)
            if obj_name == name:
                return obj
    finally:
        view.Destroy()
    raise RuntimeError("Object not found: {0}".format(name))


def get_all_vms(content, folder) -> Dict[vim.VirtualMachine, str]:
    """Return all VMs under a folder (recursively) as {vm: vm.name}."""
    start = folder if folder is not None else content.rootFolder
    view = content.viewManager.CreateContainerView(start, [vim.VirtualMachine], True)
    try:
        return {vm: getattr(vm, 'name', '<unnamed>') for vm in view.view}
    finally:
        view.Destroy()


# -----------------------------
# Original logic, adapted
# -----------------------------

def get_vms_in_datacenter(si, datacenter_name: str) -> Dict[vim.VirtualMachine, str]:
    content = si.RetrieveContent()
    datacenter = get_obj(content, [vim.Datacenter], datacenter_name)
    vm_objects = get_all_vms(content, datacenter.vmFolder)
    return vm_objects


def is_template(vm: vim.VirtualMachine) -> bool:
    try:
        return bool(vm.config and vm.config.template)
    except Exception:
        return False


def wait_and_log(seconds: int):
    if seconds <= 0:
        return
    print("Sleeping for {0} seconds to throttle operations...".format(seconds))
    time.sleep(seconds)


def power_cycle_vm(
    si,
    vm: vim.VirtualMachine,
    throttle_seconds: int,
    skip_if_off: bool = False,
):
    name = vm.name
    state = getattr(vm.runtime, 'powerState', None)
    print("\nProcessing VM: {0} (current state: {1})".format(name, state))

    # Skip templates entirely
    if is_template(vm):
        print("- Skipping template: {0}".format(name))
        return

    # Power off if needed
    if state == vim.VirtualMachinePowerState.poweredOn:
        print("- Powering off VM: {0}".format(name))
        off_task = vm.PowerOffVM_Task()
        wait_for_tasks([off_task])
        print("- Powered off: {0}".format(name))
    else:
        print("- VM already powered off: {0}".format(name))
        if skip_if_off:
            print("- Skipping power on for VM (skip-if-off enabled): {0}".format(name))
            return

    # Throttle between off and on
    wait_and_log(throttle_seconds)

    # Power on
    print("- Powering on VM: {0}".format(name))
    on_task = vm.PowerOnVM_Task()
    wait_for_tasks([on_task])
    print("- Powered on: {0}".format(name))

    # Throttle before moving to next VM
    wait_and_log(throttle_seconds)


def main():
    # Read defaults from txt/vsphere_variable.txt and inject missing CLI flags
    # before parsing so required arguments are satisfied.
    inject_vsphere_vars_into_argv()

    parser = build_arg_parser()
    args = parser.parse_args()

    try:
        si = connect_vcenter(
            host=args.host,
            user=args.user,
            password=args.password,
            disable_ssl_verification=getattr(args, 'disable_ssl_verification', True),
        )
    except SystemExit as e:
        print(str(e))
        return 1

    print("Connected to vCenter: {0}".format(args.host))
    print("Target datacenter: {0}".format(args.datacenter_name))

    try:
        vm_objects = get_vms_in_datacenter(si, args.datacenter_name)
    except RuntimeError as e:
        print("Error locating datacenter '{0}': {1}".format(args.datacenter_name, e))
        return 1

    vm_count = len(vm_objects)
    print("Discovered {0} VM objects under datacenter '{1}'.".format(vm_count, args.datacenter_name))
    if args.dry_run:
        for vm, name in vm_objects.items():
            tmpl = ' (template)' if is_template(vm) else ''
            state = getattr(vm.runtime, 'powerState', None)
            print("- {0}{1} [state: {2}]".format(name, tmpl, state))
        print("Dry run complete. No changes made.")
        return 0

    failures = 0
    processed = 0
    for vm, _name in vm_objects.items():
        try:
            power_cycle_vm(si, vm, args.sleep_seconds, skip_if_off=args.skip_if_off)
            processed += 1
        except Exception as ex:
            failures += 1
            vm_name = getattr(vm, 'name', '<unknown>')
            print("ERROR: Failed to power cycle VM '{0}': {1}".format(vm_name, ex))

    print("\nCompleted. Processed: {0}, Failures: {1}".format(processed, failures))
    return 0 if failures == 0 else 2


if __name__ == '__main__':
    sys.exit(main())


