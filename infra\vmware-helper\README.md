# vmware-helper

This repos  host pythoin CLI to manage VMs on Vmware. 
Please, fell free to add more scripts.


# Setup : 
1- Install pyvmomi: 
    ```pip3 install pyvmomi ```

The pyVmomi library itself is hosted here:
    https://github.com/vmware/pyvmomi/

# Update labels on ExtraConfig: 

2- Update labels and vm_names: 
  - options_values = {
        "jenkins_slave_labels": "statebuild kin_autotest_6 poolbuild linux64 server linuxserver"    
    }

    vm_names  = {
        "ke-worker-003"
    }

3- Runs cli by: 
  - ➜  python3 add_vm_extra_config_tags.py -s vc.dice.ad.ea.com -u {VCENTER_USER} -p {VCENTER_PASSWORD} -nossl
  
# Add new script to manage vmware:
  You can inspire from   http://vmware.github.io/pyvmomi-community-samples/
 

