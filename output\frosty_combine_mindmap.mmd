mindmap
  root((JOB: CH1-content-dev.start))
    "JOB: CH1-content-dev.frosty.start (MP build)"
      "ARTIFACT: MP build (exe, assets)"
      "FAILOVER: Use last successful MP when current fails"
      "TRIGGERS"
        "JOB: CH1-content-dev.combine.frosty.start"
    "JOB: .combine.frosty.start (split job)"
      "INPUTS"
        "LATEST: MP from CH1-content-dev.frosty.start"
        "LATEST: SP from CH1-SP-content-dev.deployment-data.start"
      "TRIGGERS"
        "POST-SUCCESS: CH1-content-dev.frosty.start"
        "POST-SUCCESS: CH1-SP-content-dev.deployment-data.start"
        "SCHEDULE: Nightly timer"
      "OUTPUT"
        "ARTIFACT: Combined MP+SP build"
    "JOB: CH1-SP-content-dev.start"
      "JOB: CH1-content-dev.copy-integrate-to.CH1-SP-content-dev.start (~15m)"
      "JOB: CH1-SP-content-dev.code.start (~50m)"
      "JOB: CH1-SP-content-dev.data.start (~1h)"
      "JOB: CH1-SP-content-dev.deployment-data.start (~2h)"
      "SP-TRIGGERED COMBINE"
        "JOB: frosty.SP-combined.start (invoke combine only)"
        "USES last successful MP if needed"
    "SHIFT UPLOAD PATHS"
      "PATH A: MP-only"
        "JOB: CH1-content-dev.shift.upload.MP (~4h)"
        "USE CASE: MP-only QV testing / decoupled deliveries"
      "PATH B: Combined"
        "JOB: CH1-content-dev.shift.upload.combined (~4h)"
        "PREREQ: Combined build ready by 04:30 UTC"
    "SLO & TIMING"
      "CUTOFF: 16:30 UK / 15:30 UTC"
      "SUBSCRIPTION: 04:30 UTC"
      "QV SHIFT: 06:00 UTC"
      "BEST-CASE TOTAL: ~10h"}