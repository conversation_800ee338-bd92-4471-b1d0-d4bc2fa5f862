# Slack Discussion Summary and Proposal Mindmap

This document summarizes key points from `txt/slack_discussion.md` and provides a mindmap of the proposed job split starting from `CH1-content-dev.start`.

## Key Points

- Objective: Ensure QV has combined builds by the first morning shift.
- Cutoff: Include all CLs up to 16:30 UK (15:30 UTC) previous day.
- Timing: QV shift 06:00 UTC; Shift subscription picks up at ~04:30 UTC; aim for Shift upload completion by then.
- Branches: Both CH1-content-dev (MP) and CH1-SP-content-dev (SP) must be green for combined builds; also monitor Stage variants next week.
- Integration: Auto integration from CH1-content-dev to CH1-SP-content-dev via `CH1-content-dev.copy-integrate-to.CH1-SP-content-dev.start`.
- Manual fallback: If CH1-content-dev breaks but SP is green, run `CH1-content-dev.frosty.start` on older MP CL while combining with latest successful SP CL to keep QV unblocked.
- Proposal: Trigger combine from SP stream for QV, or split combine into a separate job to decouple and schedule predictably.
- Suggested new jobs:
  - `frosty.SP-combined.start` (trigger only combined steps for SP-driven flows)
  - `.combine.frosty.start` (separate combine stage)
- Triggers for combine job:
  - After successful `CH1-content-dev.frosty.start` (use last successful SP deployment-data CL)
  - After `CH1-SP-content-dev.deployment-data.start` (use last successful MP frosty start CL)
  - Once-a-day scheduled run picking the latest successful CLs to ensure availability
- Benefit: Shorter per-job durations, separate MP vs combined Shift uploads, reduce cross-stream dependency for `frosty.start` jobs.
- Back-of-envelope timeline (example best case ~10h from 16:30 UK):
  1) Integrate dev->SP (~15m)
  2) SP code (~50m)
  3) SP data (~1h)
  4) SP deployment-data (~2h)
  5) Combined frosty on dev (~2h)
  6) Shift upload (~4h)

## Mermaid Mindmap: Split Starting from CH1-content-dev.start

```mermaid
mindmap
  root((CH1-content-dev.start))
    Integration
      CH1-content-dev.copy-integrate-to.CH1-SP-content-dev.start
    Frosty
      CH1-content-dev.frosty.start
      Combine (separated)
        .combine.frosty.start
          Triggers
            After CH1-content-dev.frosty.start (use last successful SP deployment-data CL)
            After CH1-SP-content-dev.deployment-data.start (use last successful MP frosty CL)
            Scheduled daily (pick latest successful CLs)
          Alternatives
            frosty.SP-combined.start (SP-driven combine)
    SP Stream
      CH1-SP-content-dev.code.start (~50m)
      CH1-SP-content-dev.data.start (~1h)
      CH1-SP-content-dev.deployment-data.start (~2h)
    Shift Uploads
      CH1-content-dev.shift.start (~4h)
      Option: separate MP-only and combined uploads
    Operational Considerations
      Cutoff 16:30 UK (15:30 UTC)
      QV shift 06:00 UTC; Shift subscription ~04:30 UTC
      Fallback: use latest SP + older MP if MP broken
      Goal: builds ready for QV morning
```

## Time Tracking

- Received: 2025-08-09 09:43 UTC
- Completed: 2025-08-09 09:44 UTC
- Duration: ~00:01 (hh:mm)
