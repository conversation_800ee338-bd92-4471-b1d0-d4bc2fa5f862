#!/usr/bin/env python3
"""
Power-cycle a fixed list of VMs by name.

- Reads vCenter connection variables from C:\\Users\\<USER>\\vscode\\txt\\vsphere_variable.txt
  keys: host, user, password (space-separated: key value)
- Loops a hardcoded list of VMs and power cycles each (off then on), with throttling.
- CLI flags allow overriding values from the variables file.
- Inspired by txt/add_vm_extra_config_tags.py patterns.

Example:
  python txt/vm_cycles_augment.py --sleep-seconds 10
  python txt/vm_cycles_augment.py -s vc.example.com -u user -p pass --sleep-seconds 5
  python txt/vm_cycles_augment.py --dry-run
"""

from __future__ import annotations

import argparse
import atexit
import ssl
import sys
import time
from pathlib import Path
from typing import Dict, List, Optional

from pyVim import connect
from pyVmomi import vim

# Hardcoded VM list as requested
VMS: List[str] = ['bct1-004d4d']


def parse_vsphere_vars_file(file_path: str) -> Dict[str, str]:
    """Parse key-value pairs from the variables file.

    Lines: "key value" separated by whitespace. Lines starting with '#' are ignored.
    The value can contain spaces; everything after the first whitespace belongs to the value.
    """
    data: Dict[str, str] = {}
    with open(file_path, "r", encoding="utf-8") as fh:
        for raw in fh:
            line = raw.strip()
            if not line or line.startswith("#"):
                continue
            parts = line.split()
            if len(parts) < 2:
                continue
            key = parts[0].strip()
            value = " ".join(parts[1:]).strip()
            data[key] = value
    return data


def resolve_credentials(args: argparse.Namespace) -> Dict[str, Optional[str]]:
    """Resolve host/user/password from CLI or variables file.

    CLI overrides file values. Returns possibly None values which will be validated later.
    """
    vars_path = Path(r"C:\Users\<USER>\vscode\txt\vsphere_variable.txt")
    kv: Dict[str, str] = {}
    if vars_path.exists():
        try:
            kv = parse_vsphere_vars_file(str(vars_path))
        except OSError as ex:
            print(f"Warning: failed to parse variables file: {ex}")

    host = args.host or kv.get("host")
    user = args.user or kv.get("user")
    password = args.password or kv.get("password")
    return {"host": host, "user": user, "password": password}


def connect_vcenter(host: str, user: str, password: str, disable_ssl_verification: bool = True):
    """Connect to vCenter and return a ServiceInstance."""
    if not host or not user or not password:
        raise SystemExit("Missing vCenter connection parameters (host/user/password).")

    if disable_ssl_verification:
        ctx = ssl.create_default_context()
        ctx.check_hostname = False
        ctx.verify_mode = ssl.CERT_NONE
        si = connect.SmartConnect(host=host, user=user, pwd=password, sslContext=ctx)
    else:
        si = connect.SmartConnect(host=host, user=user, pwd=password)

    atexit.register(connect.Disconnect, si)
    return si


def wait_for_task(task: vim.Task, poll_seconds: int = 2) -> None:
    """Wait for a vSphere task to complete, raising on error."""
    while True:
        state = getattr(task.info, "state", None)
        if state in ("success", vim.TaskInfo.State.success):  # type: ignore[attr-defined]
            return
        if state in ("error", vim.TaskInfo.State.error):  # type: ignore[attr-defined]
            fault = getattr(task.info, "error", None)
            msg = getattr(fault, "msg", str(fault)) if fault else "Unknown error"
            raise RuntimeError(f"Task failed: {msg}")
        time.sleep(poll_seconds)


def get_obj_by_name(content, vimtypes, name: str):
    """Return the first managed object of type(s) with matching name."""
    container = content.viewManager.CreateContainerView(content.rootFolder, vimtypes, True)
    try:
        for obj in container.view:
            if getattr(obj, "name", None) == name:
                return obj
    finally:
        container.Destroy()
    return None


def power_cycle_vm(vm: vim.VirtualMachine, throttle_seconds: int) -> None:
    """Power off (if powered on) then power on the VM, with throttling between operations."""
    name = vm.name

    # Skip templates entirely
    try:
        if bool(vm.config and vm.config.template):
            print(f"- Skipping template: {name}")
            return
    except Exception:  # pylint: disable=broad-except
        # If we cannot access config safely, proceed without template check
        pass

    state = getattr(vm.runtime, "powerState", None)
    print(f"Processing VM: {name} (state: {state})")

    # Power off if currently on
    try:
        if str(state).lower() == "poweredon":
            print(f"- Powering off {name} ...")
            task = vm.PowerOffVM_Task()
            wait_for_task(task)
        else:
            print(f"- VM is not powered on (state={state}); skipping power off")
    except Exception as ex:  # pylint: disable=broad-except
        print(f"Warning: power off failed for {name}: {ex}")

    # Throttle between operations
    if throttle_seconds > 0:
        time.sleep(throttle_seconds)

    # Power on
    print(f"- Powering on {name} ...")
    task = vm.PowerOnVM_Task()
    wait_for_task(task)

    if throttle_seconds > 0:
        time.sleep(throttle_seconds)


def build_arg_parser() -> argparse.ArgumentParser:
    """Build the CLI argument parser for the script."""
    parser = argparse.ArgumentParser(description="Power cycle selected VMs by name.")
    parser.add_argument("--sleep-seconds", type=int, default=5, help="Throttle between operations.")
    parser.add_argument("-s", "--host", help="vCenter host")
    parser.add_argument("-u", "--user", help="vCenter user")
    parser.add_argument("-p", "--password", help="vCenter password")
    parser.add_argument(
        "--disable-ssl-verification",
        action="store_true",
        default=True,
        help="Disable SSL verification when connecting (default: enabled)",
    )
    parser.add_argument("--dry-run", action="store_true", help="Print actions without executing")
    return parser


def main() -> int:
    """Entry point: resolve creds, connect, loop VMS and power-cycle each."""
    parser = build_arg_parser()
    args = parser.parse_args()

    creds = resolve_credentials(args)

    if args.dry_run:
        print("[DRY-RUN] Would connect to vCenter with:")
        print(f"  host={creds['host']}")
        print(f"  user={creds['user']}")
        print("  password=[HIDDEN]")
        for vm_name in VMS:
            print(f"[DRY-RUN] Would power cycle VM: {vm_name}")
        return 0

    try:
        si = connect_vcenter(
            host=creds["host"],
            user=creds["user"],
            password=creds["password"],
            disable_ssl_verification=bool(getattr(args, "disable_ssl_verification", True)),
        )
    except Exception as ex:  # pylint: disable=broad-except
        print(f"Failed to connect to vCenter: {ex}")
        return 1

    content = si.RetrieveContent()

    failures = 0
    for vm_name in VMS:
        try:
            print(f"\n--- Handling VM: {vm_name} ---")
            vm = get_obj_by_name(content, [vim.VirtualMachine], vm_name)
            if vm is None:
                print(f"WARNING: VM not found: {vm_name}")
                continue
            power_cycle_vm(vm, args.sleep_seconds)
        except Exception as ex:  # pylint: disable=broad-except
            failures += 1
            print(f"ERROR while processing '{vm_name}': {ex}")

    print(f"\nDone. Failures: {failures}")
    return 0 if failures == 0 else 3


if __name__ == "__main__":
    sys.exit(main())
