package com.ea.project.bctch1

import com.ea.exceptions.CobraException

class LibBaseline {
    static final Baseline[] disc_matrix = [
        new Baseline('CH1-bflabs-stage', 'ps5', '21082313', 'bflabs'),
        new Baseline('CH1-bflabs-stage', 'win64', '21643209', 'bflabs'),
        new Baseline('CH1-bflabs-stage', 'xbsx', '21643209', 'bflabs'),
        new Baseline('CH1-bflabs-qol', 'ps5', '21082313', 'bflabs'),
        new Baseline('CH1-bflabs-qol', 'win64', '21643209', 'bflabs'),
        new Baseline('CH1-bflabs-qol', 'xbsx', '21643209', 'bflabs'),
        new Baseline('CH1-bflabs-release', 'ps5', '21082313', 'bflabs'),
        new Baseline('CH1-bflabs-release', 'win64', '21643209', 'bflabs'),
        new Baseline('CH1-bflabs-release', 'xbsx', '21643209', 'bflabs'),
        new Baseline('CH1-content-dev-first-patch', 'ps5', '123', 'CH1-content-dev', '123', 'CH1-content-dev-disc-build', '123', 'CH1-SP-content-dev', '123', 'CH1-SP-content-dev-disc-build'),
        new Baseline('CH1-content-dev-first-patch', 'win64', '123', 'CH1-content-dev', '123', 'CH1-content-dev-disc-build', '123', 'CH1-SP-content-dev', '123', 'CH1-SP-content-dev-disc-build'),
        new Baseline('CH1-content-dev-first-patch', 'xbsx', '123', 'CH1-content-dev', '123', 'CH1-content-dev-disc-build', '123', 'CH1-SP-content-dev', '123', 'CH1-SP-content-dev-disc-build'),
        new Baseline('CH1-SP-content-dev-first-patch', 'ps5', '123', 'CH1-SP-content-dev', '123', 'CH1-SP-content-dev-disc-build'),
        new Baseline('CH1-SP-content-dev-first-patch', 'win64', '123', 'CH1-SP-content-dev', '123', 'CH1-SP-content-dev-disc-build'),
        new Baseline('CH1-SP-content-dev-first-patch', 'xbsx', '123', 'CH1-SP-content-dev', '123', 'CH1-SP-content-dev-disc-build'),
        new Baseline('CH1-event', 'ps5', '23767422', 'CH1-event-release'),
        new Baseline('CH1-event', 'win64', '23767422', 'CH1-event-release'),
        new Baseline('CH1-event', 'xbsx', '23767422', 'CH1-event-release'),
    ]
    static final Baseline[] patch_matrix = [
        new Baseline('CH1-bflabs-stage', 'ps5', '24143830', 'CH1-bflabs-release','24143830', 'CH1-bflabs-release','23421549','CH1-SP-Release','23421549','CH1-SP-Release'),
        new Baseline('CH1-bflabs-stage', 'win64', '24143830', 'CH1-bflabs-release','24143830', 'CH1-bflabs-release','23421549','CH1-SP-Release','23421549','CH1-SP-Release'),
        new Baseline('CH1-bflabs-stage', 'xbsx', '24143830', 'CH1-bflabs-release','24143830', 'CH1-bflabs-release','23421549','CH1-SP-Release','23421549','CH1-SP-Release'),
        new Baseline('CH1-bflabs-release', 'ps5', '24143830', 'CH1-bflabs-release','24143830', 'CH1-bflabs-release','23421549','CH1-SP-Release','23421549','CH1-SP-Release'),
        new Baseline('CH1-bflabs-release', 'win64', '24143830', 'CH1-bflabs-release','24143830', 'CH1-bflabs-release','23421549','CH1-SP-Release','23421549','CH1-SP-Release'),
        new Baseline('CH1-bflabs-release', 'xbsx',  '24143830', 'CH1-bflabs-release','24143830', 'CH1-bflabs-release','23421549','CH1-SP-Release','23421549','CH1-SP-Release'),
        new Baseline('CH1-bflabs-qol', 'ps5', '24143830', 'CH1-bflabs-release','24143830', 'CH1-bflabs-release','23421549','CH1-SP-Release','23421549','CH1-SP-Release'),
        new Baseline('CH1-bflabs-qol', 'win64', '24143830', 'CH1-bflabs-release','24143830', 'CH1-bflabs-release','23421549','CH1-SP-Release','23421549','CH1-SP-Release'),
        new Baseline('CH1-bflabs-qol','xbsx', '24143830', 'CH1-bflabs-release','24143830', 'CH1-bflabs-release','23421549','CH1-SP-Release','23421549','CH1-SP-Release'),
        new Baseline('CH1-event', 'ps5', '24711057', 'CH1-event','24711057', 'CH1-event'),
        new Baseline('CH1-event', 'win64', '24641779', 'CH1-event','24641779', 'CH1-event'),
        new Baseline('CH1-event','xbsx', '24641779', 'CH1-event','24641779', 'CH1-event'),
    ]

    static Baseline get_disc_baseline_for(String name, String platform) {
        return get_baseline_from_matrix(disc_matrix, name, platform)
    }

    static Baseline get_patch_baseline_for(String name, String platform) {
        return get_baseline_from_matrix(patch_matrix, name, platform)
    }

    private static Baseline get_baseline_from_matrix(Baseline[] matrix, String name, String platform) {
        for (baseline in matrix) {
            if (baseline.name == name && baseline.platform == platform) {
                return baseline
            }
        }
        throw new CobraException("No baseline configuration matches name: ${name} as well as platform: ${platform}!")
    }
}

class Baseline {
    /*
    This is the master baseline values. All other baselines should reference its values per default.
    */
    static String default_code_changelist = '123'
    static String default_code_branch = 'CH1-content-dev'
    static String default_data_changelist = '123'
    static String default_data_branch = 'CH1-content-dev'
    static String default_combine_code_changelist = ''
    static String default_combine_code_branch = ''
    static String default_combine_data_changelist = ''
    static String default_combine_data_branch = ''
    String name, platform, code_changelist, code_branch, data_changelist, data_branch
    String combine_code_changelist, combine_code_branch, combine_data_changelist, combine_data_branch

    // EXCEPTIONAL Constructor, for combined builds
    Baseline(name, platform, code_changelist, code_branch, data_changelist, data_branch, combine_code_changelist, combine_code_branch, combine_data_changelist, combine_data_branch) {
        this.name = name
        this.platform = platform
        this.code_changelist = code_changelist
        this.code_branch = code_branch
        this.data_changelist = data_changelist
        this.data_branch = data_branch
        this.combine_code_changelist = combine_code_changelist
        this.combine_code_branch = combine_code_branch
        this.combine_data_changelist = combine_data_changelist
        this.combine_data_branch = combine_data_branch
    }
    // EXCEPTIONAL Constructor
    Baseline(name, platform, code_changelist, code_branch, data_changelist, data_branch) {
        this(name, platform, code_changelist, code_branch, data_changelist, data_branch, default_combine_code_changelist, default_combine_code_branch, default_combine_data_changelist, default_combine_data_branch)
    }
    // EXCEPTIONAL Constructor
    Baseline(name, platform, changelist, branch) {
        this(name, platform, changelist, branch, changelist, branch)
    }
    // DEFAULT Constructor
    Baseline(name, platform) {
        this(name, platform, default_code_changelist, default_code_branch, default_data_changelist, default_data_branch)
    }
}
