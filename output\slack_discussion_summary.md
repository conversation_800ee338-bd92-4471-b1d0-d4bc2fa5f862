# QV Build SLO Discussion — Summary and Mindmap

## Key Points
- Goal/SLO: Deliver builds for the first QV shift that include all CLs up to 16:30 UK time (15:30 UTC) the previous day.
- Branch scope: QV tests combined builds; both branches must be green:
  - CH1-Content-dev and CH1-SP-Content-Dev (and, as needed next week, CH1-stage & CH1-SP-stage).
- Timing:
  - Cutoff: 15:30 UTC (previous day)
  - QV shift starts: ~06:00 UTC
  - Shift subscription starts: ~04:30 UTC → Shift upload must be completed by then.
  - Available time from cutoff to subscription: ~13 hours.
- Current integration/build coupling:
  - Integration: CH1-content-dev → CH1-SP-content-dev via `CH1-content-dev.copy-integrate-to.CH1-SP-content-dev.start`.
  - `frosty.start` currently runs on CH1-content-dev (main stream). If CH1-content-dev is broken, SP delivery is impacted.
  - Manual mitigation used before: run `CH1-content-dev.frosty.start` on an older CL for MP while taking newer SP, allowing QV to test with fresh SP.exe and older MP.
- Proposal direction:
  - Trigger combined builds from the SP stream for QV builds; Shift uploads just the combined SP stream. If dev line (MP) breaks but SP is green, QV can still proceed using last successful MP.
  - Add a timer/scheduled combine job to always produce something for Shift to upload: pick the latest successful SP + MP builds and combine them unless already built.
  - Anticipated `Shift.Start` improvements should reduce variability (target ~3h vs up to 7h today), enabling better scheduling of the “backup” SP combine.
- Detailed split proposal (Kaan): separate combine jobs into `.combine.frosty.start` and decouple from `frosty.start`:
  - Triggers:
    - After `CH1-content-dev.frosty.start` (takes last successful SP deployment-data CL)
    - After `CH1-SP-content-dev.deployment-data.start` (takes last successful MP frosty start CL)
    - Plus once-a-day scheduled trigger using the latest successful CLs from both jobs to ensure a combined build exists.
  - Also consider separating MP Shift uploads from combined Shift uploads to shorten individual upload durations.
  - Benefit: eliminates tight dependency between MP and SP streams for `frosty.start`; more resilient pipeline.
- Additional note: May need a new `frosty.SP-combined.start` that only runs combination steps, potentially invoked from SP stream.

## Expected Durations (example path for last SP submit at 16:30 UK)
1) Auto integration CH1-content-dev → CH1-SP-content-dev: ~15 min
2) `CH1-SP-content-dev.code.start`: ~50 min
3) `CH1-SP-content-dev.data.start`: ~1 hr
4) `CH1-SP-content-dev.deployment-data.start`: ~2 hrs
5) `CH1-content-dev.frosty.combined` (or SP-triggered combine): ~2 hrs
6) `CH1-content-dev.shift.start` (upload): ~4 hrs
Total best-case: ~10 hrs → completes ~02:30 UTC, in time for 04:30 UTC subscription and 06:00 UTC QV shift.

## Mindmap — Split/Resilient Pipeline (rooted at CH1-content-dev.start)
```mermaid
mindmap
  root((JOB: CH1-content-dev.start))
    "JOB: CH1-content-dev.frosty.start (MP build)"
      "ARTIFACT: MP build (exe, assets)"
      "FAILOVER: Use last successful MP when current fails"
      "TRIGGERS"
        "JOB: CH1-content-dev.combine.frosty.start"
    "JOB: .combine.frosty.start (split job)"
      "INPUTS"
        "LATEST: MP from CH1-content-dev.frosty.start"
        "LATEST: SP from CH1-SP-content-dev.deployment-data.start"
      "TRIGGERS"
        "POST-SUCCESS: CH1-content-dev.frosty.start"
        "POST-SUCCESS: CH1-SP-content-dev.deployment-data.start"
        "SCHEDULE: Nightly timer"
      "OUTPUT"
        "ARTIFACT: Combined MP+SP build"
    "JOB: CH1-SP-content-dev.start"
      "JOB: CH1-content-dev.copy-integrate-to.CH1-SP-content-dev.start (~15m)"
      "JOB: CH1-SP-content-dev.code.start (~50m)"
      "JOB: CH1-SP-content-dev.data.start (~1h)"
      "JOB: CH1-SP-content-dev.deployment-data.start (~2h)"
      "SP-TRIGGERED COMBINE"
        "JOB: frosty.SP-combined.start (invoke combine only)"
        "USES last successful MP if needed"
    "SHIFT UPLOAD PATHS"
      "PATH A: MP-only"
        "JOB: CH1-content-dev.shift.upload.MP (~4h)"
        "USE CASE: MP-only QV testing / decoupled deliveries"
      "PATH B: Combined"
        "JOB: CH1-content-dev.shift.upload.combined (~4h)"
        "PREREQ: Combined build ready by 04:30 UTC"
    "SLO & TIMING"
      "CUTOFF: 16:30 UK / 15:30 UTC"
      "SUBSCRIPTION: 04:30 UTC"
      "QV SHIFT: 06:00 UTC"
      "BEST-CASE TOTAL: ~10h"
```

## Actionable Next Steps
- Define and implement `.combine.frosty.start` with the two post-success triggers and a nightly schedule.
- Add `frosty.SP-combined.start` to trigger combine-only from SP stream when beneficial.
- Configure Shift job(s) to allow separate MP and combined uploads to reduce durations and improve predictability.
- Establish guardrails to select last successful MP when current MP is failing, ensuring QV always receives a build.