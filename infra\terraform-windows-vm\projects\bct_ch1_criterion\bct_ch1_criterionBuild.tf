# *************************************************************
#  Sets up the initial needs to point to our vSphere server
# *************************************************************
# Point to our datacenter
# https://www.terraform.io/docs/providers/vsphere/d/datacenter.html
#
# Notes:
# Datastore Cluster: https://www.terraform.io/docs/providers/vsphere/r/datastore_cluster.html
# Individual Datastores: https://www.terraform.io/docs/providers/vsphere/d/datastore.html
# Resource Pools: https://www.terraform.io/docs/providers/vsphere/d/resource_pool.html
# Networking: https://www.terraform.io/docs/providers/vsphere/d/network.html
# Templates: https://www.terraform.io/docs/providers/vsphere/r/virtual_machine.html
# Folder Managment: https://www.terraform.io/docs/providers/vsphere/r/folder.html
# UUID Usage: https://www.terraform.io/docs/providers/random/r/id.html
# Count Usage: https://www.terraform.io/intro/examples/count.html

# *************************************************************
# Terraform state legacy artifactory backup.
# https://www.terraform.io/docs/backends/types/artifactory.html
# *************************************************************
terraform {
  backend "http" {
  }
}

locals {
  module_settings = {
    # STATEBUILD/POOLBUILD BCT-CH1-DEV-JENKINS
    "ch1_statebuild_poolbuild_win64_001"   = { datastore = "OH-PSTORE01_6", vm_count = "1", labels = "statebuild_criterion poolbuild_criterion win64" }
    "ch1_statebuild_poolbuild_ps5_001"     = { datastore = "OH-PSTORE02_7", vm_count = "1", labels = "statebuild_criterion poolbuild_criterion ps5" }
    "ch1_statebuild_poolbuild_xbsx_001"    = { datastore = "OH-PSTORE02_7", vm_count = "1", labels = "statebuild_criterion poolbuild_criterion xbsx" }
    "ch1_statebuild_poolbuild_servers_001" = { datastore = "OH-PSTORE02_7", vm_count = "1", labels = "statebuild_criterion poolbuild_criterion server linuxserver" }
    "ch1_statebuild_poolbuild_linux64_001" = { datastore = "OH-PSTORE02_7", vm_count = "1", labels = "statebuild_criterion poolbuild_criterion linux64" }

    # STATEBUILD/POOLBUILD BCT-CH1-REL-JENKINS
    "ch1_release_statebuild_poolbuild_win64_001"   = { datastore = "OH-PSTORE02_6", vm_count = "2", labels = "statebuild_criterion poolbuild_criterion win64", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "ch1_release_statebuild_poolbuild_ps5_001"     = { datastore = "OH-PSTORE02_6", vm_count = "2", labels = "statebuild_criterion poolbuild_criterion ps5", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "ch1_release_statebuild_poolbuild_xbsx_001"    = { datastore = "OH-PSTORE02_6", vm_count = "2", labels = "statebuild_criterion poolbuild_criterion xbsx", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "ch1_release_statebuild_poolbuild_servers_001" = { datastore = "OH-PSTORE02_6", vm_count = "2", labels = "statebuild_criterion poolbuild_criterion server linuxserver", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "ch1_release_statebuild_poolbuild_linux64_001" = { datastore = "OH-PSTORE02_6", vm_count = "2", labels = "statebuild_criterion poolbuild_criterion linux64", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "ch1_release_statebuild_poolbuild_win64_004"   = { datastore = "OH-PSTORE02_4", vm_count = "2", labels = "statebuild_criterion poolbuild_criterion win64", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "ch1_release_statebuild_poolbuild_ps5_004"     = { datastore = "OH-PSTORE02_4", vm_count = "2", labels = "statebuild_criterion poolbuild_criterion ps5", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "ch1_release_statebuild_poolbuild_xbsx_004"    = { datastore = "OH-PSTORE02_4", vm_count = "2", labels = "statebuild_criterion poolbuild_criterion xbsx", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "ch1_release_statebuild_poolbuild_servers_004" = { datastore = "OH-PSTORE02_4", vm_count = "2", labels = "statebuild_criterion poolbuild_criterion server linuxserver", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "ch1_release_statebuild_poolbuild_linux64_004" = { datastore = "OH-PSTORE02_4", vm_count = "2", labels = "statebuild_criterion poolbuild_criterion linux64", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }

    # CH1-RELEASE/CH1-BFLABS-RELEASE CODE
    "criterion_2_2_ch1_bflabs_release_code_linux64_final_001"         = { datastore = "OH-PSTORE02_2", vm_count = "1", labels = "criterion_2_2 CH1-bflabs-release CH1-release code linux64 final", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "criterion_2_2_ch1_bflabs_release_code_linuxserver_final_001"     = { datastore = "OH-PSTORE02_2", vm_count = "1", labels = "criterion_2_2 CH1-bflabs-release CH1-release code linux64server final", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "criterion_2_5_ch1_bflabs_release_code_ps5_final_001"             = { datastore = "OH-PSTORE02_5", vm_count = "1", labels = "criterion_2_5 CH1-bflabs-release CH1-release code ps5 final", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "criterion_2_5_ch1_bflabs_release_code_ps5_release_001"           = { datastore = "OH-PSTORE02_5", vm_count = "1", labels = "criterion_2_5 CH1-bflabs-release CH1-release code ps5 release", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "criterion_2_5_ch1_bflabs_release_code_ps5_retail_001"            = { datastore = "OH-PSTORE02_5", vm_count = "1", labels = "criterion_2_5 CH1-bflabs-release CH1-release code ps5 retail", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "criterion_2_5_ch1_bflabs_release_code_ps5_performance_001"       = { datastore = "OH-PSTORE02_5", vm_count = "1", labels = "criterion_2_5 CH1-bflabs-release CH1-release code ps5 performance", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "criterion_2_2_ch1_bflabs_release_code_tool_release_001"          = { datastore = "OH-PSTORE02_2", vm_count = "1", labels = "criterion_2_2 CH1-bflabs-release CH1-release code tool release", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "criterion_2_2_ch1_bflabs_release_code_win64game_final_001"       = { datastore = "OH-PSTORE02_2", vm_count = "1", labels = "criterion_2_2 CH1-bflabs-release CH1-release code win64game final", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "criterion_2_2_ch1_bflabs_release_code_win64game_release_001"     = { datastore = "OH-PSTORE02_2", vm_count = "1", labels = "criterion_2_2 CH1-bflabs-release CH1-release code win64game release", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "criterion_2_2_ch1_bflabs_release_code_win64game_retail_001"      = { datastore = "OH-PSTORE02_2", vm_count = "1", labels = "criterion_2_2 CH1-bflabs-release CH1-release code win64game retail", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "criterion_2_5_ch1_bflabs_release_code_win64game_performance_001" = { datastore = "OH-PSTORE02_5", vm_count = "1", labels = "criterion_2_5 CH1-bflabs-release CH1-release code win64game performance", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "criterion_2_2_ch1_bflabs_release_code_win64server_final_001"     = { datastore = "OH-PSTORE02_2", vm_count = "1", labels = "criterion_2_2 CH1-bflabs-release CH1-release code win64server final", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "criterion_2_5_ch1_bflabs_release_code_xbsx_final_001"            = { datastore = "OH-PSTORE02_5", vm_count = "1", labels = "criterion_2_5 CH1-bflabs-release CH1-release code xbsx final", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "criterion_2_5_ch1_bflabs_release_code_xbsx_release_001"          = { datastore = "OH-PSTORE02_5", vm_count = "1", labels = "criterion_2_5 CH1-bflabs-release CH1-release code xbsx release", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "criterion_2_5_ch1_bflabs_release_code_xbsx_retail_001"           = { datastore = "OH-PSTORE02_5", vm_count = "1", labels = "criterion_2_5 CH1-bflabs-release CH1-release code xbsx retail", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "criterion_2_5_ch1_bflabs_release_code_xbsx_performance_001"      = { datastore = "OH-PSTORE02_5", vm_count = "1", labels = "criterion_2_5 CH1-bflabs-release CH1-release code xbsx performance", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }

    # CH1-RELEASE CODE
    "ch1_release_code_ps5_performance_001"       = { datastore = "OH-PSTORE02_5", vm_count = "1", labels = "criterion_02_5 CH1-release code ps5 performance", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "ch1_release_code_win64game_performance_001" = { datastore = "OH-PSTORE02_5", vm_count = "1", labels = "criterion_02_5 CH1-release code win64game performance", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "ch1_release_code_xbsx_performance_001"      = { datastore = "OH-PSTORE02_5", vm_count = "1", labels = "criterion_02_5 CH1-release code xbsx performance", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }

    # CH1-RELEASE/CH1-BFLABS-RELEASE DATA/PATCHDATA
    "criterion_2_1_ch1_bflabs_release_data_webexport_001"  = { datastore = "OH-PSTORE02_1", vm_count = "1", labels = "criterion_2_1 CH1-bflabs-release CH1-release data webexport", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "criterion_2_1_ch1_bflabs_release_patchdata_ps5_001"   = { datastore = "OH-PSTORE02_1", vm_count = "1", labels = "criterion_2_1 CH1-bflabs-release CH1-release patchdata ps5", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "criterion_2_1_ch1_bflabs_release_patchdata_win64_001" = { datastore = "OH-PSTORE02_1", vm_count = "1", labels = "criterion_2_1 CH1-bflabs-release CH1-release patchdata win64", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "criterion_2_1_ch1_bflabs_release_patchdata_xbsx_001"  = { datastore = "OH-PSTORE02_1", vm_count = "1", labels = "criterion_2_1 CH1-bflabs-release CH1-release patchdata xbsx", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }

    # CH1-RELEASE PATCHDATA
    "ch1_release_patchdata_ps5_001"   = { datastore = "OH-PSTORE02_5", vm_count = "2", labels = "criterion_02_5 CH1-release patchdata ps5", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "ch1_release_patchdata_win64_001" = { datastore = "OH-PSTORE02_5", vm_count = "2", labels = "criterion_02_5 CH1-release patchdata win64", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "ch1_release_patchdata_xbsx_001"  = { datastore = "OH-PSTORE02_5", vm_count = "2", labels = "criterion_02_5 CH1-release patchdata xbsx", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "ch1_release_webexport_001"       = { datastore = "OH-PSTORE02_5", vm_count = "1", labels = "criterion_02_5 CH1-release webexport", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }

    # CH1-RELEASE/CH1-BFLABS-RELEASE FROSTY
    "criterion_6_ch1_bflabs_release_frosty_linux64_final_files_001"  = { datastore = "OH-PSTORE01_5", vm_count = "1", labels = "criterion_6 CH1-bflabs-release CH1-release frosty linux64 final files", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "criterion_6_ch1_bflabs_release_frosty_linser_final_digital_001" = { datastore = "OH-PSTORE01_5", vm_count = "1", labels = "criterion_6 CH1-bflabs-release CH1-release frosty linuxserver final digital", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "criterion_6_ch1_bflabs_release_frosty_server_final_files_001"   = { datastore = "OH-PSTORE01_5", vm_count = "1", labels = "criterion_6 CH1-bflabs-release CH1-release frosty server final files", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "criterion_6_ch1_bflabs_release_frosty_ps5_final_files_001"      = { datastore = "OH-PSTORE01_5", vm_count = "1", labels = "criterion_6 CH1-bflabs-release CH1-release frosty ps5 final files", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "criterion_6_ch1_bflabs_release_frosty_win64_final_files_001"    = { datastore = "OH-PSTORE01_5", vm_count = "1", labels = "criterion_6 CH1-bflabs-release CH1-release frosty win64 final files", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "criterion_6_ch1_bflabs_release_frosty_xbsx_final_files_001"     = { datastore = "OH-PSTORE01_5", vm_count = "1", labels = "criterion_6 CH1-bflabs-release CH1-release frosty xbsx final files", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }

    # CH1-RELEASE FROSTY
    "ch1_release_frosty_ps5_performance_files_001"   = { datastore = "OH-PSTORE02_5", vm_count = "1", labels = "criterion_02_5 CH1-release frosty ps5 performance files", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "ch1_release_frosty_ps5_retail_combine_001"      = { datastore = "OH-PSTORE02_7", vm_count = "1", labels = "criterion_02_5 CH1-release frosty ps5 retail combine", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "ch1_release_frosty_win64_performance_files_001" = { datastore = "OH-PSTORE02_5", vm_count = "1", labels = "criterion_02_5 CH1-release frosty win64 performance files", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "ch1_release_frosty_win64_retail_combine_001"    = { datastore = "OH-PSTORE02_7", vm_count = "1", labels = "criterion_02_5 CH1-release frosty win64 retail combine", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "ch1_release_frosty_xbsx_performance_files_001"  = { datastore = "OH-PSTORE02_5", vm_count = "1", labels = "criterion_02_5 CH1-release frosty xbsx performance files", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "ch1_release_frosty_xbsx_retail_combine_001"     = { datastore = "OH-PSTORE02_7", vm_count = "1", labels = "criterion_02_5 CH1-release frosty xbsx retail combine", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "ch1_release_frosty_win64_final_combine_001"     = { datastore = "OH-PSTORE02_7", vm_count = "1", labels = "criterion_02_7 CH1-release frosty win64 final combine", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "ch1_content_dev_frosty_ps5_final_combine_001"   = { datastore = "OH-PSTORE02_7", vm_count = "1", labels = "criterion_02_7 CH1-release frosty ps5 final combine", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "ch1_content_dev_frosty_xbsx_final_combine_001"  = { datastore = "OH-PSTORE02_7", vm_count = "1", labels = "criterion_02_7 CH1-release frosty xbsx final combine", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }

    # CH1-RELEASE/CH1-BFLABS-RELEASE PATCHFROSTY
    "ch1_bflabs_release_patchfrosty_ps5_retail_combine_001"   = { datastore = "OH-PSTORE01_5", vm_count = "1", labels = "criterion_6 CH1-bflabs-release CH1-release patchfrosty ps5 retail combine", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "ch1_bflabs_release_patchfrosty_win64_retail_combine_001" = { datastore = "OH-PSTORE01_5", vm_count = "1", labels = "criterion_6 CH1-bflabs-release CH1-release patchfrosty win64 retail combine", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "ch1_bflabs_release_patchfrosty_xbsx_retail_combine_001"  = { datastore = "OH-PSTORE01_5", vm_count = "1", labels = "criterion_6 CH1-bflabs-release CH1-release patchfrosty xbsx retail combine", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "ch1_bflabs_release_patchfrosty_ps5_final_combine_001"    = { datastore = "OH-PSTORE02_4", vm_count = "1", labels = "criterion_02_4 CH1-bflabs-release CH1-release patchfrosty ps5 final combine", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "ch1_bflabs_release_patchfrosty_win64_final_combine_001"  = { datastore = "OH-PSTORE02_4", vm_count = "1", labels = "criterion_02_4 CH1-bflabs-release CH1-release patchfrosty win64 final combine", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "ch1_bflabs_release_patchfrosty_xbsx_final_combine_001"   = { datastore = "OH-PSTORE02_4", vm_count = "1", labels = "criterion_02_4 CH1-bflabs-release CH1-release patchfrosty xbsx final combine", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }

    # CH1-BFLABS-QOL PATCHFROSTY
    "criterion_6_ch1_bflabs_qol_patchfrosty_ps5_final_digital_001"     = { datastore = "OH-PSTORE02_6", vm_count = "1", labels = "criterion_6 CH1-bflabs-qol patchfrosty ps5 final digital", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "criterion_6_ch1_bflabs_qol_patchfrosty_ps5_release_digital_001"   = { datastore = "OH-PSTORE02_6", vm_count = "1", labels = "criterion_6 CH1-bflabs-qol patchfrosty ps5 retail digital", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "criterion_6_ch1_bflabs_qol_patchfrosty_win64_final_digital_001"   = { datastore = "OH-PSTORE02_6", vm_count = "1", labels = "criterion_6 CH1-bflabs-qol patchfrosty win64 final digital", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "criterion_6_ch1_bflabs_qol_patchfrosty_win64_release_digital_001" = { datastore = "OH-PSTORE02_6", vm_count = "1", labels = "criterion_6 CH1-bflabs-qol patchfrosty win64 retail digital", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "criterion_6_ch1_bflabs_qol_patchfrosty_win64_final_combine_1"     = { datastore = "OH-PSTORE02_4", vm_count = "1", labels = "criterion_6 CH1-bflabs-qol patchfrosty win64 final combine", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "criterion_6_ch1_bflabs_qol_patchfrosty_xbsx_final_digital_001"    = { datastore = "OH-PSTORE02_6", vm_count = "1", labels = "criterion_6 CH1-bflabs-qol patchfrosty xbsx final digital", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "criterion_6_ch1_bflabs_qol_patchfrosty_xbsx_release_digital_001"  = { datastore = "OH-PSTORE02_6", vm_count = "1", labels = "criterion_6 CH1-bflabs-qol patchfrosty xbsx retail digital", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "criterion_6_ch1_bflabs_qol_patchfrosty_xbsx_final_combine_1"      = { datastore = "OH-PSTORE02_4", vm_count = "1", labels = "criterion_6 CH1-bflabs-qol patchfrosty xbsx final combine", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "criterion_6_ch1_bflabs_qol_patchfrosty_xbsx_final_combine_2"      = { datastore = "OH-PSTORE02_4", vm_count = "1", labels = "criterion_6 CH1-bflabs-qol patchfrosty xbsx final combine", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }

    # CH1-BFLABS-QOL PATCHFROSTY COMBINE
    "criterion_6_ch1_bflabs_qol_patchfrosty_ps5_final_labs_combine_001"    = { datastore = "OH-PSTORE02_6", vm_count = "1", labels = "criterion_6 CH1-bflabs-qol patchfrosty ps5 final labs combine", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "criterion_6_ch1_bflabs_qol_patchfrosty_ps5_retail_labs_combine_001"   = { datastore = "OH-PSTORE02_6", vm_count = "1", labels = "criterion_6 CH1-bflabs-qol patchfrosty ps5 retail labs combine", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "criterion_6_ch1_bflabs_qol_patchfrosty_win64_retail_labs_combine_001" = { datastore = "OH-PSTORE02_6", vm_count = "1", labels = "criterion_6 CH1-bflabs-qol patchfrosty win64 labs retail combine", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "criterion_6_ch1_bflabs_qol_patchfrosty_xbsx_retail_labs_combine_001"  = { datastore = "OH-PSTORE02_6", vm_count = "1", labels = "criterion_6 CH1-bflabs-qol patchfrosty xbsx labs retail combine", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }

    # CH1-QOL CODE
    "criterion_ch1_qol_code_linux64_final_001"         = { datastore = "OH-PSTORE02_3", vm_count = "1", labels = "criterion CH1-qol CH1-bflabs-qol code linux64 final", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "criterion_ch1_qol_code_linuxserver_final_001"     = { datastore = "OH-PSTORE02_3", vm_count = "1", labels = "criterion CH1-qol CH1-bflabs-qol code linux64server final", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "criterion_ch1_qol_code_ps5_final_001"             = { datastore = "OH-PSTORE02_3", vm_count = "1", labels = "criterion CH1-qol CH1-bflabs-qol code ps5 final", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "criterion_ch1_qol_code_ps5_performance_001"       = { datastore = "OH-PSTORE02_3", vm_count = "1", labels = "criterion CH1-qol CH1-bflabs-qol code ps5 performance", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "criterion_ch1_qol_code_ps5_release_001"           = { datastore = "OH-PSTORE02_3", vm_count = "1", labels = "criterion CH1-qol CH1-bflabs-qol code ps5 release", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "criterion_ch1_qol_code_ps5_retail_001"            = { datastore = "OH-PSTORE02_3", vm_count = "1", labels = "criterion CH1-qol CH1-bflabs-qol code ps5 retail", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "criterion_ch1_qol_code_tool_release_001"          = { datastore = "OH-PSTORE02_3", vm_count = "1", labels = "criterion CH1-qol CH1-bflabs-qol code tool release", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "criterion_ch1_qol_code_win64game_final_001"       = { datastore = "OH-PSTORE02_3", vm_count = "1", labels = "criterion CH1-qol CH1-bflabs-qol code win64game final", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "criterion_ch1_qol_code_win64game_performance_001" = { datastore = "OH-PSTORE02_3", vm_count = "1", labels = "criterion CH1-qol CH1-bflabs-qol code win64game performance", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "criterion_ch1_qol_code_win64game_release_001"     = { datastore = "OH-PSTORE02_3", vm_count = "1", labels = "criterion CH1-qol CH1-bflabs-qol code win64game release", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "criterion_ch1_qol_code_win64game_retail_001"      = { datastore = "OH-PSTORE02_3", vm_count = "1", labels = "criterion CH1-qol CH1-bflabs-qol code win64game retail", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "criterion_ch1_qol_code_win64server_final_001"     = { datastore = "OH-PSTORE02_3", vm_count = "1", labels = "criterion CH1-qol CH1-bflabs-qol code win64server final", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "criterion_ch1_qol_code_xbsx_final_001"            = { datastore = "OH-PSTORE02_3", vm_count = "1", labels = "criterion CH1-qol CH1-bflabs-qol code xbsx final", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "criterion_ch1_qol_code_xbsx_performance_001"      = { datastore = "OH-PSTORE02_3", vm_count = "1", labels = "criterion CH1-qol CH1-bflabs-qol code xbsx performance", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "criterion_ch1_qol_code_xbsx_release_001"          = { datastore = "OH-PSTORE02_3", vm_count = "1", labels = "criterion CH1-qol CH1-bflabs-qol code xbsx release", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "criterion_ch1_qol_code_xbsx_retail_001"           = { datastore = "OH-PSTORE02_3", vm_count = "1", labels = "criterion CH1-qol CH1-bflabs-qol code xbsx retail", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }

    # CH1-QOL DATA
    "criterion_ch1_qol_data_ps5_001"       = { datastore = "OH-PSTORE01_6", vm_count = "2", labels = "criterion CH1-qol CH1-bflabs-qol data ps5", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "criterion_ch1_qol_data_server_001"    = { datastore = "OH-PSTORE01_6", vm_count = "2", labels = "criterion CH1-qol CH1-bflabs-qol data server", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "criterion_ch1_qol_data_win64_001"     = { datastore = "OH-PSTORE01_6", vm_count = "2", labels = "criterion CH1-qol CH1-bflabs-qol data win64", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "criterion_ch1_qol_data_xbsx_001"      = { datastore = "OH-PSTORE01_6", vm_count = "2", labels = "criterion CH1-qol CH1-bflabs-qol data xbsx", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "criterion_ch1_qol_data_webexport_001" = { datastore = "OH-PSTORE01_6", vm_count = "1", labels = "criterion CH1-qol CH1-bflabs-qol data webexport", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }

    # CH1-QOL PATCHDATA
    "criterion_ch1_qol_patchdata_ps5_001"   = { datastore = "OH-PSTORE02_1", vm_count = "2", labels = "criterion CH1-qol CH1-bflabs-qol patchdata ps5", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "criterion_ch1_qol_patchdata_win64_001" = { datastore = "OH-PSTORE02_1", vm_count = "2", labels = "criterion CH1-qol CH1-bflabs-qol patchdata win64", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "criterion_ch1_qol_patchdata_xbsx_001"  = { datastore = "OH-PSTORE02_1", vm_count = "2", labels = "criterion CH1-qol CH1-bflabs-qol patchdata xbsx", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }

    # CH1-QOL FROSTY
    "criterion_ch1_qol_frosty_linux64_final_files_001"       = { datastore = "OH-PSTORE01_6", vm_count = "1", labels = "criterion CH1-qol CH1-bflabs-qol frosty linux64 final files", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "criterion_ch1_qol_frosty_linuxserver_final_digital_001" = { datastore = "OH-PSTORE01_6", vm_count = "1", labels = "criterion CH1-qol CH1-bflabs-qol frosty linuxserver final digital", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "criterion_ch1_qol_frosty_ps5_final_combine_001"         = { datastore = "OH-PSTORE01_6", vm_count = "1", labels = "criterion CH1-qol CH1-bflabs-qol frosty ps5 final combine", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "criterion_ch1_qol_frosty_ps5_final_digital_001"         = { datastore = "OH-PSTORE01_6", vm_count = "1", labels = "criterion CH1-qol CH1-bflabs-qol frosty ps5 final digital", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "criterion_ch1_qol_frosty_ps5_final_files_001"           = { datastore = "OH-PSTORE01_6", vm_count = "1", labels = "criterion CH1-qol CH1-bflabs-qol frosty ps5 final files", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "criterion_ch1_qol_frosty_ps5_performance_files_001"     = { datastore = "OH-PSTORE01_6", vm_count = "1", labels = "criterion CH1-qol CH1-bflabs-qol frosty ps5 performance files", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "criterion_ch1_qol_frosty_ps5_retail_combine_001"        = { datastore = "OH-PSTORE01_6", vm_count = "1", labels = "criterion CH1-qol CH1-bflabs-qol frosty ps5 retail combine", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "criterion_ch1_qol_frosty_ps5_retail_digital_001"        = { datastore = "OH-PSTORE01_6", vm_count = "1", labels = "criterion CH1-qol CH1-bflabs-qol frosty ps5 retail digital", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "criterion_ch1_qol_frosty_server_final_digital_001"      = { datastore = "OH-PSTORE01_6", vm_count = "1", labels = "criterion CH1-qol CH1-bflabs-qol frosty server final digital", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "criterion_ch1_qol_frosty_server_final_files_001"        = { datastore = "OH-PSTORE01_6", vm_count = "1", labels = "criterion CH1-qol CH1-bflabs-qol frosty server final files", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "criterion_ch1_qol_frosty_win64_final_combine_001"       = { datastore = "OH-PSTORE01_6", vm_count = "1", labels = "criterion CH1-qol CH1-bflabs-qol frosty win64 final combine", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "criterion_ch1_qol_frosty_win64_final_combine_002"       = { datastore = "OH-PSTORE01_6", vm_count = "1", labels = "criterion CH1-qol CH1-bflabs-qol frosty win64 final combine", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "criterion_ch1_qol_frosty_win64_final_digital_001"       = { datastore = "OH-PSTORE01_6", vm_count = "1", labels = "criterion CH1-qol CH1-bflabs-qol frosty win64 final digital", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "criterion_ch1_qol_frosty_win64_final_digital_002"       = { datastore = "OH-PSTORE01_6", vm_count = "1", labels = "criterion CH1-qol CH1-bflabs-qol frosty win64 final digital", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "criterion_ch1_qol_frosty_win64_final_files_001"         = { datastore = "OH-PSTORE01_6", vm_count = "1", labels = "criterion CH1-qol CH1-bflabs-qol frosty win64 final files", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "criterion_ch1_qol_frosty_win64_performance_files_001"   = { datastore = "OH-PSTORE01_6", vm_count = "1", labels = "criterion CH1-qol CH1-bflabs-qol frosty win64 performance files", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "criterion_ch1_qol_frosty_win64_performance_files_002"   = { datastore = "OH-PSTORE01_6", vm_count = "1", labels = "criterion CH1-qol CH1-bflabs-qol frosty win64 performance files", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "criterion_ch1_qol_frosty_win64_release_digital_001"     = { datastore = "OH-PSTORE01_6", vm_count = "1", labels = "criterion CH1-qol CH1-bflabs-qol frosty win64 release digital", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "criterion_ch1_qol_frosty_win64_release_digital_002"     = { datastore = "OH-PSTORE01_6", vm_count = "1", labels = "criterion CH1-qol CH1-bflabs-qol frosty win64 release digital", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "criterion_ch1_qol_frosty_win64_retail_combine_001"      = { datastore = "OH-PSTORE01_6", vm_count = "1", labels = "criterion CH1-qol CH1-bflabs-qol frosty win64 retail combine", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "criterion_ch1_qol_frosty_win64_retail_combine_002"      = { datastore = "OH-PSTORE01_6", vm_count = "1", labels = "criterion CH1-qol CH1-bflabs-qol frosty win64 retail combine", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "criterion_ch1_qol_frosty_win64_retail_digital_003"      = { datastore = "OH-PSTORE01_6", vm_count = "1", labels = "criterion CH1-qol CH1-bflabs-qol frosty win64 retail digital", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "criterion_ch1_qol_frosty_win64_retail_digital_004"      = { datastore = "OH-PSTORE01_6", vm_count = "1", labels = "criterion CH1-qol CH1-bflabs-qol frosty win64 retail digital", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "criterion_ch1_qol_frosty_xbsx_final_combine_001"        = { datastore = "OH-PSTORE01_6", vm_count = "1", labels = "criterion CH1-qol CH1-bflabs-qol frosty xbsx final combine", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "criterion_ch1_qol_frosty_xbsx_final_combine_002"        = { datastore = "OH-PSTORE01_6", vm_count = "1", labels = "criterion CH1-qol CH1-bflabs-qol frosty xbsx final combine", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "criterion_ch1_qol_frosty_xbsx_final_digital_001"        = { datastore = "OH-PSTORE01_6", vm_count = "1", labels = "criterion CH1-qol CH1-bflabs-qol frosty xbsx final digital", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "criterion_ch1_qol_frosty_xbsx_final_digital_002"        = { datastore = "OH-PSTORE01_6", vm_count = "1", labels = "criterion CH1-qol CH1-bflabs-qol frosty xbsx final digital", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "criterion_ch1_qol_frosty_xbsx_final_files_001"          = { datastore = "OH-PSTORE01_6", vm_count = "1", labels = "criterion CH1-qol CH1-bflabs-qol frosty xbsx final files", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "criterion_ch1_qol_frosty_xbsx_final_files_002"          = { datastore = "OH-PSTORE01_6", vm_count = "1", labels = "criterion CH1-qol CH1-bflabs-qol frosty xbsx final files", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "criterion_ch1_qol_frosty_xbsx_performance_files_001"    = { datastore = "OH-PSTORE01_6", vm_count = "1", labels = "criterion CH1-qol CH1-bflabs-qol frosty xbsx performance files", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "criterion_ch1_qol_frosty_xbsx_performance_files_002"    = { datastore = "OH-PSTORE01_6", vm_count = "1", labels = "criterion CH1-qol CH1-bflabs-qol frosty xbsx performance files", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "criterion_ch1_qol_frosty_xbsx_retail_combine_001"       = { datastore = "OH-PSTORE01_6", vm_count = "1", labels = "criterion CH1-qol CH1-bflabs-qol frosty xbsx retail combine", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "criterion_ch1_qol_frosty_xbsx_retail_combine_002"       = { datastore = "OH-PSTORE01_6", vm_count = "1", labels = "criterion CH1-qol CH1-bflabs-qol frosty xbsx retail combine", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "criterion_ch1_qol_frosty_xbsx_retail_digital_001"       = { datastore = "OH-PSTORE01_6", vm_count = "1", labels = "criterion CH1-qol CH1-bflabs-qol frosty xbsx retail digital", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "criterion_ch1_qol_frosty_xbsx_retail_digital_002"       = { datastore = "OH-PSTORE01_6", vm_count = "1", labels = "criterion CH1-qol CH1-bflabs-qol frosty xbsx retail digital", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }

    # CH1-BFLABS-RELEASE STEAM BUILD
    "ch1_release_frosty_win64_final_steam_001"  = { datastore = "OH-PSTORE02_5", vm_count = "1", labels = "criterion_02_5 CH1-release CH1-bflabs-release frosty win64 final steam_build", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "ch1_release_frosty_win64_retail_steam_001" = { datastore = "OH-PSTORE02_7", vm_count = "1", labels = "criterion_02_5 CH1-release CH1-bflabs-release frosty win64 retail steam_build", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "ch1_release_frosty_win64_final_steam_002"  = { datastore = "OH-PSTORE01_5", vm_count = "1", labels = "criterion_6 CH1-release CH1-bflabs-release frosty win64 final steam_build", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "ch1_release_frosty_win64_retail_steam_002" = { datastore = "OH-PSTORE01_5", vm_count = "1", labels = "criterion_6 CH1-release CH1-bflabs-release frosty win64 retail steam_build", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }

    # CH1-BFLABS-RELEASE PATCH STEAM BUILD
    "ch1_release_patchfrosty_win64_final_steam_001"  = { datastore = "OH-PSTORE01_5", vm_count = "1", labels = "criterion_02_5 CH1-release CH1-bflabs-release patchfrosty win64 final steam_build", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "ch1_release_patchfrosty_win64_retail_steam_001" = { datastore = "OH-PSTORE01_5", vm_count = "1", labels = "criterion_02_5 CH1-release CH1-bflabs-release patchfrosty win64 retail steam_build", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }

    # CH1-QOL STEAM BUILD
    "ch1_qol_steam_build_final_001"              = { datastore = "OH-PSTORE02_3", vm_count = "1", labels = "win64 CH1-qol CH1-bflabs-qol final steam_build frosty", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "ch1_qol_steam_build_retail_001"             = { datastore = "OH-PSTORE02_3", vm_count = "1", labels = "win64 CH1-qol CH1-bflabs-qol retail steam_build frosty", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "ch1_qol_patchfrosty_steam_build_final_001"  = { datastore = "OH-PSTORE02_2", vm_count = "1", labels = "win64 CH1-qol CH1-bflabs-qol final steam_build patchfrosty", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "ch1_qol_patchfrosty_steam_build_retail_001" = { datastore = "OH-PSTORE02_2", vm_count = "1", labels = "win64 CH1-qol CH1-bflabs-qol retail steam_build patchfrosty", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }

    # CH1-EVENT-RELEASE CODE
    "ch1_event_release_code_linux64_final_001"       = { datastore = "OH-PSTORE02_2", vm_count = "1", labels = "criterion_02_2 CH1-event-release code linux64 final", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "ch1_event_release_code_linux64server_final_001" = { datastore = "OH-PSTORE02_2", vm_count = "1", labels = "criterion_02_2 CH1-event-release code linux64server final", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "ch1_event_release_code_ps5_final_001"           = { datastore = "OH-PSTORE02_2", vm_count = "1", labels = "criterion_02_2 CH1-event-release code ps5 final", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "ch1_event_release_code_ps5_retail_001"          = { datastore = "OH-PSTORE02_2", vm_count = "1", labels = "criterion_02_2 CH1-event-release code ps5 retail", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "ch1_event_release_code_tool_release_001"        = { datastore = "OH-PSTORE02_2", vm_count = "1", labels = "criterion_02_2 CH1-event-release code tool release", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "ch1_event_release_code_win64game_final_001"     = { datastore = "OH-PSTORE02_2", vm_count = "1", labels = "criterion_02_2 CH1-event-release code win64game final", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "ch1_event_release_code_win64game_retail_001"    = { datastore = "OH-PSTORE02_2", vm_count = "1", labels = "criterion_02_2 CH1-event-release code win64game retail", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "ch1_event_release_code_win64server_final_001"   = { datastore = "OH-PSTORE02_2", vm_count = "1", labels = "criterion_02_2 CH1-event-release code win64server final", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "ch1_event_release_code_xbsx_final_001"          = { datastore = "OH-PSTORE02_2", vm_count = "1", labels = "criterion_02_2 CH1-event-release code xbsx final", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "ch1_event_release_code_xbsx_retail_001"         = { datastore = "OH-PSTORE02_2", vm_count = "1", labels = "criterion_02_2 CH1-event-release code xbsx retail", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }

    # CH1-EVENT-RELEASE DATA
    "ch1_event_release_data_linux64_001" = { datastore = "OH-PSTORE02_7", vm_count = "1", labels = "criterion_02_7 CH1-event-release data linux64", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "ch1_event_release_data_ps5_001"     = { datastore = "OH-PSTORE02_7", vm_count = "1", labels = "criterion_02_7 CH1-event-release data ps5", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "ch1_event_release_data_win64_001"   = { datastore = "OH-PSTORE02_2", vm_count = "2", labels = "criterion_02_2 CH1-event-release data win64", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "ch1_event_release_data_server_001"  = { datastore = "OH-PSTORE02_2", vm_count = "1", labels = "criterion_02_2 CH1-event-release data server", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "ch1_event_release_data_xbsx_001"    = { datastore = "OH-PSTORE02_2", vm_count = "1", labels = "criterion_02_2 CH1-event-release data xbsx", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "ch1_event_release_webexport_001"    = { datastore = "OH-PSTORE02_2", vm_count = "1", labels = "criterion_02_2 CH1-event-release webexport", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }

    # CH1-EVENT-RELEASE FROSTY
    "ch1_event_release_frosty_win64_final_files_001"    = { datastore = "OH-PSTORE02_1", vm_count = "1", labels = "criterion_02_1 CH1-event-release frosty win64 final files", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "ch1_event_release_frosty_win64_retail_digital_001" = { datastore = "OH-PSTORE02_1", vm_count = "1", labels = "criterion_02_1 CH1-event-release frosty win64 retail digital", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "ch1_event_release_frosty_win64_final_digital_001"  = { datastore = "OH-PSTORE02_1", vm_count = "1", labels = "criterion_02_1 CH1-event-release frosty win64 final digital", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "ch1_event_releasefrosty_win64_retail_steam_001"    = { datastore = "OH-PSTORE01_3", vm_count = "1", labels = "criterion_01_3 CH1-event-release frosty win64 retail steam_build", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "ch1_event_releasefrosty_win64_final_steam_001"     = { datastore = "OH-PSTORE01_3", vm_count = "1", labels = "criterion_01_3 CH1-event-release frosty win64 final steam_build", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "ch1_event_release_frosty_ps5_final_files_001"      = { datastore = "OH-PSTORE01_1", vm_count = "1", labels = "criterion_02_1 CH1-event-release frosty ps5 final files", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "ch1_event_release_frosty_ps5_retail_digital_001"   = { datastore = "OH-PSTORE01_1", vm_count = "1", labels = "criterion_02_1 CH1-event-release frosty ps5 retail digital", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "ch1_event_release_frosty_ps5_final_digital_001"    = { datastore = "OH-PSTORE01_1", vm_count = "1", labels = "criterion_02_1 CH1-event-release frosty ps5 final digital", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "ch1_event_release_frosty_xbsx_final_files_001"     = { datastore = "OH-PSTORE01_1", vm_count = "1", labels = "criterion_02_1 CH1-event-release frosty xbsx final files", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "ch1_event_release_frosty_xbsx_retail_digital_001"  = { datastore = "OH-PSTORE01_1", vm_count = "1", labels = "criterion_02_1 CH1-event-release frosty xbsx retail digital", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "ch1_event_release_frosty_xbsx_final_digital_001"   = { datastore = "OH-PSTORE01_1", vm_count = "1", labels = "criterion_02_1 CH1-event-release frosty xbsx final digital", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "ch1_event_release_frosty_server_final_files_001"   = { datastore = "OH-PSTORE02_1", vm_count = "1", labels = "criterion_02_1 CH1-event-release frosty server final files", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "ch1_event_release_frosty_linuxserver_final_001"    = { datastore = "OH-PSTORE02_1", vm_count = "1", labels = "criterion_02_1 CH1-event-release frosty linuxserver final digital", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "ch1_event_release_frosty_linuxserver_retail_001"   = { datastore = "OH-PSTORE02_1", vm_count = "1", labels = "criterion_02_1 CH1-event-release frosty linuxserver digital retail", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "ch1_event_release_frosty_linux64_final_001"        = { datastore = "OH-PSTORE01_3", vm_count = "1", labels = "criterion_01_3 CH1-event-release frosty linux64 final files", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }

    # CH1-EVENT CODE
    "ch1_event_code_linux64_final_001"         = { datastore = "OH-PSTORE01_3", vm_count = "1", labels = "criterion_01_3 CH1-event code linux64 final", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "ch1_event_code_linux64server_final_001"   = { datastore = "OH-PSTORE01_3", vm_count = "1", labels = "criterion_01_3 CH1-event code linux64server final", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "ch1_event_code_ps5_final_001"             = { datastore = "OH-PSTORE01_3", vm_count = "1", labels = "criterion_01_3 CH1-event code ps5 final", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "ch1_event_code_ps5_retail_001"            = { datastore = "OH-PSTORE01_3", vm_count = "1", labels = "criterion_01_3 CH1-event code ps5 retail", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "ch1_event_code_ps5_release_001"           = { datastore = "OH-PSTORE01_3", vm_count = "1", labels = "criterion_01_3 CH1-event code ps5 release", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "ch1_event_code_ps5_performance_001"       = { datastore = "OH-PSTORE01_3", vm_count = "1", labels = "criterion_01_3 CH1-event code ps5 performance", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "ch1_event_code_tool_release_001"          = { datastore = "OH-PSTORE01_3", vm_count = "1", labels = "criterion_01_3 CH1-event code tool release", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "ch1_event_code_win64game_final_001"       = { datastore = "OH-PSTORE01_3", vm_count = "1", labels = "criterion_01_3 CH1-event code win64game final", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "ch1_event_code_win64game_retail_001"      = { datastore = "OH-PSTORE01_3", vm_count = "1", labels = "criterion_01_3 CH1-event code win64game retail", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "ch1_event_code_win64game_release_001"     = { datastore = "OH-PSTORE01_3", vm_count = "1", labels = "criterion_01_3 CH1-event code win64game release", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "ch1_event_code_win64game_performance_001" = { datastore = "OH-PSTORE01_3", vm_count = "1", labels = "criterion_01_3 CH1-event code win64game performance", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "ch1_event_code_win64server_final_001"     = { datastore = "OH-PSTORE01_3", vm_count = "1", labels = "criterion_01_3 CH1-event code win64server final", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "ch1_event_code_xbsx_final_001"            = { datastore = "OH-PSTORE01_3", vm_count = "1", labels = "criterion_01_3 CH1-event code xbsx final", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "ch1_event_code_xbsx_retail_001"           = { datastore = "OH-PSTORE01_3", vm_count = "1", labels = "criterion_01_3 CH1-event code xbsx retail", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "ch1_event_code_xbsx_release_001"          = { datastore = "OH-PSTORE01_3", vm_count = "1", labels = "criterion_01_3 CH1-event code xbsx release", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "ch1_event_code_xbsx_performance_001"      = { datastore = "OH-PSTORE01_3", vm_count = "1", labels = "criterion_01_3 CH1-event code xbsx performance", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }

    # CH1-EVENT PATCHDATA/WEBEXPORT
    "ch1_event_patchdata_ps5_001"   = { datastore = "OH-PSTORE01_4", vm_count = "1", labels = "criterion_01_4 CH1-event patchdata ps5", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "ch1_event_patchdata_win64_001" = { datastore = "OH-PSTORE01_4", vm_count = "2", labels = "criterion_01_4 CH1-event patchdata win64", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "ch1_event_patchdata_xbsx_001"  = { datastore = "OH-PSTORE01_4", vm_count = "1", labels = "criterion_01_4 CH1-event patchdata xbsx", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "ch1_event_webexport_001"       = { datastore = "OH-PSTORE01_4", vm_count = "1", labels = "criterion_01_4 CH1-event webexport", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }

    # CH1-EVENT FROSTY
    "ch1_event_frosty_win64_final_files_001"     = { datastore = "OH-PSTORE01_1", vm_count = "1", labels = "criterion_01_1 CH1-event frosty win64 final files", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "ch1_event_frosty_win64_retail_steam_001"    = { datastore = "OH-PSTORE01_1", vm_count = "1", labels = "criterion_01_1 CH1-event frosty win64 retail steam_build", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "ch1_event_frosty_win64_final_steam_001"     = { datastore = "OH-PSTORE01_1", vm_count = "1", labels = "criterion_01_1 CH1-event frosty win64 final steam_build", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "ch1_event_frosty_ps5_final_files_001"       = { datastore = "OH-PSTORE01_1", vm_count = "1", labels = "criterion_01_1 CH1-event frosty ps5 final files", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "ch1_event_frosty_ps5_retail_files_001"      = { datastore = "OH-PSTORE01_1", vm_count = "1", labels = "criterion_01_1 CH1-event frosty ps5 retail files", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "ch1_event_frosty_xbsx_final_files_001"      = { datastore = "OH-PSTORE01_1", vm_count = "1", labels = "criterion_01_1 CH1-event frosty xbsx final files", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "ch1_event_frosty_xbsx_retail_files_001"     = { datastore = "OH-PSTORE01_1", vm_count = "1", labels = "criterion_01_1 CH1-event frosty xbsx retail files", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "ch1_event_frosty_server_final_files_001"    = { datastore = "OH-PSTORE01_1", vm_count = "1", labels = "criterion_01_1 CH1-event frosty server final files", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "ch1_event_frosty_server_final_digital_001"  = { datastore = "OH-PSTORE01_1", vm_count = "1", labels = "criterion_01_1 CH1-event frosty server final digital", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "ch1_event_frosty_linuxserver_final_dig_001" = { datastore = "OH-PSTORE01_1", vm_count = "1", labels = "criterion_01_1 CH1-event frosty linuxserver final digital", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "ch1_event_frosty_linux64_final_files_001"   = { datastore = "OH-PSTORE01_1", vm_count = "1", labels = "criterion_01_1 CH1-event frosty linux64 final files", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }

    # CH1-EVENT PATCHFROSTY
    "ch1_event_patchfrosty_win64_retail_digital_001"    = { datastore = "OH-PSTORE02_1", vm_count = "1", labels = "criterion_02_1 CH1-event patchfrosty win64 retail digital", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "ch1_event_patchfrosty_win64_final_digital_001"     = { datastore = "OH-PSTORE02_1", vm_count = "1", labels = "criterion_02_1 CH1-event patchfrosty win64 final digital", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "ch1_event_patchfrosty_ps5_retail_digital_001"      = { datastore = "OH-PSTORE02_1", vm_count = "1", labels = "criterion_02_1 CH1-event patchfrosty ps5 retail digital", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "ch1_event_patchfrosty_ps5_final_digital_001"       = { datastore = "OH-PSTORE02_1", vm_count = "1", labels = "criterion_02_1 CH1-event patchfrosty ps5 final digital", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "ch1_event_patchfrosty_ps5_performance_digital_001" = { datastore = "OH-PSTORE02_1", vm_count = "1", labels = "criterion_02_1 CH1-event patchfrosty ps5 performance digital", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "ch1_event_patchfrosty_xbsx_retail_digital_001"     = { datastore = "OH-PSTORE02_1", vm_count = "1", labels = "criterion_02_1 CH1-event patchfrosty xbsx retail digital", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "ch1_event_patchfrosty_xbsx_final_digital_001"      = { datastore = "OH-PSTORE02_1", vm_count = "1", labels = "criterion_02_1 CH1-event patchfrosty xbsx final digital", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }

    # CH1 INTEGRATIONS
    "ch1_release_to_ch1_stage_001"                           = { datastore = "OH-PSTORE02_7", vm_count = "1", labels = "CH1-release-to-CH1-stage-branch-guardian", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "ch1_stage_to_ch1_content_dev_branch_guardian_001"       = { datastore = "OH-PSTORE02_1", vm_count = "1", labels = "CH1-stage-to-CH1-content-dev-branch-guardian", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "ch1_sp_stage_to_ch1_sp_content_dev_branch_guardian_001" = { datastore = "OH-PSTORE01_1", vm_count = "1", labels = "CH1-SP-stage-to-CH1-SP-content-dev-branch-guardian", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "ch1_stage_to_ch1_sp_stage_001"                          = { datastore = "OH-PSTORE02_1", vm_count = "1", labels = "CH1-stage-to-CH1-SP-stage", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }

    # CH1 Steam Upload VM
    "ch1_steam_build_upload_001" = { datastore = "OH-PSTORE02_6", vm_count = "1", labels = "criterion_02_6 steam_upload", role = "https://bct-ch1-rel-jenkins.cobra.dre.ea.com" }
    "ch1_steam_build_upload_002" = { datastore = "OH-PSTORE02_6", vm_count = "2", labels = "criterion_02_6 steam_upload" }

    #QVE Test Icepick
    "ch1_qve_test_xb_vm_001" = { datastore = "OH-PSTORE02_1", vm_count = "1", labels = "poolbuild_qve_test_xbox xbsx", role = "https://bct-ch1-autotest-jenkins.cobra.dre.ea.com" }
    "ch1_qve_test_ps_vm_001" = { datastore = "OH-PSTORE02_1", vm_count = "1", labels = "poolbuild_qve_test_ps5 ps5", role = "https://bct-ch1-autotest-jenkins.cobra.dre.ea.com" }

    #LKG autotest
    "bct_lkgautotest_criterion_ps5_001"   = { datastore = "OH-PSTORE02_7", vm_count = "2", labels = "lkg_auto_criterion ps5", role = "https://bct-autotest-jenkins.cobra.dre.ea.com" }
    "bct_lkgautotest_criterion_win64_001" = { datastore = "OH-PSTORE02_7", vm_count = "2", labels = "lkg_auto_criterion win64", role = "https://bct-autotest-jenkins.cobra.dre.ea.com" }
    "bct_lkgautotest_criterion_xbsx_001"  = { datastore = "OH-PSTORE02_7", vm_count = "2", labels = "lkg_auto_criterion xbsx", role = "https://bct-autotest-jenkins.cobra.dre.ea.com" }
  }
}


module "dynamic_local_module_primary" {
  source   = "../../modules/windows_attach_module_v3.5"
  for_each = local.module_settings

  vsphere_datastore       = each.value.datastore
  vm_count                = each.value.vm_count
  vm_prefix               = try(each.value.vm_prefix, "bct1-")
  jenkins_slave_labels    = each.value.labels
  role                    = try(each.value.role, "https://bct-ch1-dev-jenkins.cobra.dre.ea.com")
  vsphere_compute_cluster = try(each.value.compute_cluster, "DRE UK Build Hardware")
  ram_count               = try(each.value.ram_count, 65536)
  jenkins_websocket       = try(each.value.jenkins_websocket, "disabled")
  vsphere_template        = "win10_22H2-cobra-v1.1101.0b76e848"
  vsphere_network         = "Buildfarm Network"
  vsphere_datacenter      = "Onslow"
  vsphere_folder          = "Build/dre-terraform-nodes/bct_ch1_nodes"
  domain_admin            = var.domain_admin
  domain_admin_password   = var.domain_password
  local_admin_user        = var.local_username
  local_admin_password    = var.local_password
  project_dir             = var.project_dir
  project_name            = "bct_ch1_criterion"
  commit_sha              = var.commit_sha
  commit_user             = var.commit_user
  commit_url              = var.commit_url
  disk_size               = "700"
  domain_name             = "dice.ad.ea.com"
  domain_ou               = "OU=BuildMonkeys,OU=Computers,OU=Stockholm,OU=Offices,DC=dice,DC=ad,DC=ea,DC=com"
  hardware_version        = var.hardware_version
  local_admin_group       = var.local_admin_group
}

# *************************************************************
#  Setting up the dynamic output needed for downstream pipelines
# *************************************************************
# Notes:
# For Expressions: https://www.terraform.io/docs/language/expressions/for.html
# Key Functions: https://www.terraform.io/docs/language/functions/keys.html
# flatten Function: https://www.terraform.io/docs/language/functions/flatten.html
# Output Values: https://www.terraform.io/docs/language/values/outputs.html
# Local Values: https://www.terraform.io/docs/language/values/locals.html
#
# The solution outputs the same way as previously in output.tf
# example:
# node_name_uuids = [
#  {
#   "id"   = "JF5D"
#   "name" = "245e43"
#   },
#
# *************************************************************
#  Dynamic Output, check CONTRIBUTING.md before editing here.
# *************************************************************
locals {
  nodes_output = flatten([
    for mod in keys(local.module_settings) : [
      for node in module.dynamic_local_module_primary[mod].nodes : [
        {
          name              = node.name
          id                = node.id
          custom_attributes = node.custom_attributes
        }
      ]
    ]
  ])
  node_name_uuids_output = flatten([
    for mod in keys(local.module_settings) : [
      for node in module.dynamic_local_module_primary[mod].node_name_uuids : [
        {
          name = node.hex
          id   = node.id
        }
      ]
    ]
  ])
}

output "nodes" {
  value = local.nodes_output
}

output "node_name_uuids" {
  value = local.node_name_uuids_output
}
