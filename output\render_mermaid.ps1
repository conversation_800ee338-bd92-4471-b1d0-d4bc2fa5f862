param(
  [string]$Input = "c:\\Users\\<USER>\\vscode\\output\\frosty_combine_mindmap.mmd",
  [string]$OutSvg = "c:\\Users\\<USER>\\vscode\\output\\frosty_combine_mindmap.svg",
  [string]$OutPng = "c:\\Users\\<USER>\\vscode\\output\\frosty_combine_mindmap.png"
)

function Write-Info($msg) { Write-Host "[info] $msg" -ForegroundColor Cyan }
function Write-Warn($msg) { Write-Host "[warn] $msg" -ForegroundColor Yellow }
function Write-Err($msg)  { Write-Host "[error] $msg" -ForegroundColor Red }

Write-Info "Input: $Input"
Write-Info "SVG Out: $OutSvg"
Write-Info "PNG Out: $OutPng"

# Try mermaid-cli if available
$mermaid = (Get-Command mmdc -ErrorAction SilentlyContinue)
if ($mermaid) {
  Write-Info "Found mermaid-cli at $($mermaid.Source). Rendering..."
  mmdc -i $Input -o $OutSvg -b transparent -t default
  mmdc -i $Input -o $OutPng -b transparent -t default
  Write-Info "Rendered via mmdc."
  exit 0
}

# Try npx mermaid-cli
$npm = (Get-Command npx -ErrorAction SilentlyContinue)
if ($npm) {
  Write-Info "mmdc not found, using npx @mermaid-js/mermaid-cli"
  npx -y @mermaid-js/mermaid-cli -i $Input -o $OutSvg -b transparent -t default
  npx -y @mermaid-js/mermaid-cli -i $Input -o $OutPng -b transparent -t default
  Write-Info "Rendered via npx."
  exit 0
}

Write-Warn "Neither mmdc nor npx found. Skipping render."
Write-Warn "You can install globally: npm install -g @mermaid-js/mermaid-cli"
Write-Warn "Then rerun: powershell -ExecutionPolicy Bypass -File .\\render_mermaid.ps1"
exit 1